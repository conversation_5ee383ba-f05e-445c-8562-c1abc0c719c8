# User Interface Design Goals

## Overall UX Vision
Clean, Jupiter-style trading interface optimized for speed and precision. Single-screen workflow that minimizes clicks between token input and position monitoring. Professional trading aesthetic with real-time data prominence and instant feedback for all actions. Zero-distraction design focused on execution speed over feature discovery.

## Key Interaction Paradigms  
- **One-Click Execution**: Buy and sell actions require single confirmation
- **Live Data First**: All price, PnL, and position data updates in real-time
- **Progressive Disclosure**: Exit strategy configuration available but not mandatory for quick trades
- **Status-Driven UI**: Clear visual indicators for position states (monitoring, triggered, executing)
- **Keyboard Shortcuts**: Power-user shortcuts for common actions (quick close, strategy toggle)
  - Essential actions: `ESC` (close position), `B` (buy panel), `Space` (acknowledge alerts)
  - Navigation: `1-9` (switch positions), `?` (shortcut overlay)
  - Non-customizable for MVP (simpler implementation)

## Core Screens and Views
- **Watchlist Page**: Token discovery and monitoring with market data, notes, and direct swap routing
- **Trading Panel**: Token input, amount/slippage settings, Jupiter route preview, buy execution
- **Position Dashboard**: Active positions with real-time PnL, exit strategy status, manual close buttons  
- **Exit Strategy Editor**: Multi-tier take profit configuration, stop loss/trailing stop settings
- **Trade History**: Completed trades with detailed analytics and performance metrics
- **Settings**: Telegram integration, default strategies, priority fee preferences
- **Alerts Panel**: Real-time notifications for executions, triggers, and system events

## Accessibility: None
MVP focused on single power-user, accessibility features deferred to post-launch iterations.

## Branding
Minimal, professional trading aesthetic. Dark theme optimized for extended screen time. Jupiter-inspired clean lines with emphasis on data readability and action button prominence. No flashy animations that could interfere with execution speed.

## Target Device and Platforms: Web Responsive  
Primary: Desktop browser for serious trading sessions
Secondary: Mobile responsive for position monitoring and emergency closes
- ≤768px: Card view, hide advanced stats, show only PnL/price/close
- Touch-friendly: Swipe left on position card to reveal close button
- Critical alerts: Full-screen modal (can't miss stop-loss notifications)
- Alert styling: Top-right toast notifications with color coding
- Green (TP filled), Red (SL hit), Blue (buy confirmed), Yellow (warnings)
- Auto-dismiss after 5s except for critical alerts (SL/errors)
