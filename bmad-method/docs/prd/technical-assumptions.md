# Technical Assumptions

## Repository Structure: Monorepo
**Rationale:** Single repository with frontend, backend, and shared utilities managed together for simplified development and deployment.

## Service Architecture: Monolith with Job Queue Architecture
**Rationale:** Express backend with BullMQ job queue system provides reliability for critical trading operations while maintaining simplicity and cost efficiency.

## Testing Requirements: Unit + Integration with Jest
**Rationale:** Comprehensive testing of trading logic, API integrations, and job queue operations essential for financial application reliability.

## Frontend Technology Stack
- **Framework**: Next.js with TypeScript for fast, SEO-friendly React applications
- **UI Components**: shadcn/ui for professional, customizable component library
- **Styling**: Tailwind CSS for rapid, consistent, responsive design
- **State Management**: Zustand for lightweight, efficient app state management
- **Charts/Visualization**: Recharts for real-time price charts, PnL graphs, and analytics
- **Notifications**: Sonner/toast for instant in-app user feedback and alerts

## Backend Technology Stack
- **Runtime**: Node.js with TypeScript for unified language and excellent async performance
- **Framework**: Express for lightweight, flexible API development
- **Database ORM**: Prisma for type-safe PostgreSQL interactions and migrations
- **Job Queue**: BullMQ with Redis for reliable trading automation and task processing
- **Logging**: Pino for high-performance structured logging and audit trails
- **Testing**: Jest for comprehensive unit and integration testing

## Database and Caching
- **Primary Database**: PostgreSQL for reliable trade data, user configs, and analytics
- **Cache/Queue Store**: Redis for BullMQ job management and real-time data caching
- **ORM**: Prisma for type-safe database operations and schema management

## API Integrations and Blockchain
- **DEX Aggregation**: Jupiter Aggregator API & SDK for optimal trade routing
- **Blockchain**: @solana/web3.js for core Solana interactions and transaction handling
- **Wallet Integration**: @solana/wallet-adapter for local keypair and hardware wallet support
- **RPC/Webhooks**: Helius for fast transaction relay and real-time blockchain event monitoring
- **Price Data**: CoinMarketCap DEX API for efficient batch price feeds (up to 30 tokens/call)
- **Notifications**: Telegram Bot API for instant trade alerts and system notifications

## Infrastructure and DevOps
- **Development**: Docker Compose for local development environment consistency
- **Frontend Deployment**: Vercel for Next.js hosting with automatic CI/CD and previews
- **Backend Deployment**: Railway for Node.js, PostgreSQL, and Redis hosting
- **Monitoring**: Bull Board for visual job queue management and debugging
- **Code Quality**: Prettier + ESLint for consistent code formatting and error prevention
- **CI/CD**: GitHub Actions for automated testing, building, and deployment

## Additional Technical Assumptions and Requests
- **Queue Management**: BullMQ handles all critical trading operations (exit triggers, notifications) with retry logic and priority handling
- **Real-time Updates**: Redis caching enables sub-second price updates and position status changes
- **Error Handling**: Comprehensive error logging with Pino for debugging and audit requirements
- **Security**: Local wallet signing with secure private key handling, no browser extension dependencies
- **Scalability**: Architecture supports multiple concurrent positions with efficient batch processing
- **Monitoring**: Bull Board dashboard provides real-time visibility into trading automation health
- **Documentation**: Markdown/Notion for system documentation, API specs, and troubleshooting guides
