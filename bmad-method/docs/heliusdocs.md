# Helius API Documentation

# getPriorityFeeEstimate

> Calculate optimal priority fee recommendations for Solana transactions based on real-time
network conditions. This advanced API analyzes historical fee data and current congestion
levels to provide precise fee estimates for different priority levels.


## OpenAPI

````yaml openapi/priority-fee-api/getPriorityFeeEstimate.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    enum:
                      - '2.0'
                    default: '2.0'
                    description: >-
                      JSON-RPC version identifier for the Solana RPC API
                      request, must be "2.0"
              id:
                allOf:
                  - type: string
                    default: '1'
                    description: Client-generated identifier for the request
              method:
                allOf:
                  - type: string
                    enum:
                      - getPriorityFeeEstimate
                    default: getPriorityFeeEstimate
                    description: The RPC method name
              params:
                allOf:
                  - type: array
                    description: Parameters for the method call
                    default:
                      - transaction: >-
                          LxzhDW7TapzziJVHEGPh1QjmZB6kjNqmvuJ9gmihBGEkzw2N8ospDq32UZdVmKWzRZqKuyvhp7xmLvsmmHa3MfxVKpYoLV9cPkw5isHnHgDUwLSMsDaZ4dLEULexXAvuV9k6fLD2LMhFKM6gqH6j69GQLAm1g4e3z5ZVZN6pmJdSmZ4PqKcwNn4sS7E3Abp1hV59uBJB9i4jEdEAh28rZ8WCeNizzEmrJZFhatFFFGSDsk23jDPEjbkMcoRWXKf1WthFScC2S6Wz284Dtjqp7kW8eybV3DpmN9DtBbcfFNQPtUwmiBZCKszdYym5AjJvRHiUKUdMwFpC3toPnMvTmKZ9iHQtzZRkg5xBufRtUN5eVYJfdw2DxzH6RfqhC2rAjfSbfJA4wmaq9f5eUe2iEjmqvne6r85PPcHBJzyqkVtAyUFtTc9DfU8UiM9rFhQ2UB71M6m5UCsC6EwowMw5k8iF8rL7iPCLdubVdy8S58DPLNG4E4rdosev1T63YdRWSgEwBZh7iX4zGBA9AKAm3B9itCSxLSL46Y6uZgVku9UXsMcWWz3URoa6hRbo55CYPLhrVeqjqX5DsXpaG6QkngbEaESQUnkakE1AFkYXmNXEd2m3sLRTYB2o5UTkwkJS2u5sJHyYqAvXr3vFZr7qAYFLD3LwS5tsdb45ZQVQwsbnNddMHgkpmSqLHS6Fv1epxqHbzpRCU1tgMsriApVWC3pj2LLD41HxkV8aKvkVyHgJFACUtbvRBZAasHf1F71Gz3qZNTdh3efWsZJRXnfxKPbATU7NTMaMUL8JjknfoVwp3
                    items:
                      type: object
                      properties:
                        transaction:
                          type: string
                          description: >
                            Base58 or Base64 encoded Solana transaction for
                            which to estimate optimal priority fees.

                            The API will analyze the specific instructions and
                            accounts in this transaction to provide

                            accurate fee estimates based on current network
                            conditions and computational complexity.
                        accountKeys:
                          type: array
                          description: >-
                            Array of Solana account public keys to estimate
                            priority fees for (alternative to providing a
                            transaction)
                          items:
                            type: string
                            description: Base58 encoded public key of a Solana account
                        options:
                          $ref: '#/components/schemas/PriorityFeeOptions'
            required: true
            refIdentifier: '#/components/schemas/PriorityFeeEstimateRequest'
        examples:
          transactionExample:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getPriorityFeeEstimate
              params:
                - transaction: >-
                    LxzhDW7TapzziJVHEGPh1QjmZB6kjNqmvuJ9gmihBGEkzw2N8ospDq32UZdVmKWzRZqKuyvhp7xmLvsmmHa3MfxVKpYoLV9cPkw5isHnHgDUwLSMsDaZ4dLEULexXAvuV9k6fLD2LMhFKM6gqH6j69GQLAm1g4e3z5ZVZN6pmJdSmZ4PqKcwNn4sS7E3Abp1hV59uBJB9i4jEdEAh28rZ8WCeNizzEmrJZFhatFFFGSDsk23jDPEjbkMcoRWXKf1WthFScC2S6Wz284Dtjqp7kW8eybV3DpmN9DtBbcfFNQPtUwmiBZCKszdYym5AjJvRHiUKUdMwFpC3toPnMvTmKZ9iHQtzZRkg5xBufRtUN5eVYJfdw2DxzH6RfqhC2rAjfSbfJA4wmaq9f5eUe2iEjmqvne6r85PPcHBJzyqkVtAyUFtTc9DfU8UiM9rFhQ2UB71M6m5UCsC6EwowMw5k8iF8rL7iPCLdubVdy8S58DPLNG4E4rdosev1T63YdRWSgEwBZh7iX4zGBA9AKAm3B9itCSxLSL46Y6uZgVku9UXsMcWWz3URoa6hRbo55CYPLhrVeqjqX5DsXpaG6QkngbEaESQUnkakE1AFkYXmNXEd2m3sLRTYB2o5UTkwkJS2u5sJHyYqAvXr3vFZr7qAYFLD3LwS5tsdb45ZQVQwsbnNddMHgkpmSqLHS6Fv1epxqHbzpRCU1tgMsriApVWC3pj2LLD41HxkV8aKvkVyHgJFACUtbvRBZAasHf1F71Gz3qZNTdh3efWsZJRXnfxKPbATU7NTMaMUL8JjknfoVwp3/SsAUIMHFzjQMQmoiQMSL1q+S+r9dLR55BgaWAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAG3fbh12Whk9nL4UbO63msHLSF7V9bN5E6jPWFfv8AqQECAwABDAIAAAAEAAAAAAAA
          accountKeysExample:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getPriorityFeeEstimate
              params:
                - accountKeys:
                    - 2CiBfRKcERi2GgYn83UaGo1wFaYHHrXGGfnDaa2hxdEA
                  options:
                    includeAllPriorityFeeLevels: true
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    enum:
                      - '2.0'
                    description: JSON-RPC version identifier
              id:
                allOf:
                  - type: string
                    description: Client-generated identifier matching the request
              result:
                allOf:
                  - type: object
                    description: >-
                      Result object containing comprehensive Solana priority fee
                      estimates
                    properties:
                      priorityFeeEstimate:
                        type: number
                        format: float
                        description: >
                          Estimated optimal priority fee in microlamports for
                          the requested Solana transaction,

                          calculated based on recent blockchain activity and
                          computational requirements.
                      priorityFeeLevels:
                        type: object
                        description: >
                          Detailed fee estimates for different priority levels
                          on the Solana blockchain (in microlamports),

                          allowing developers to choose the optimal balance
                          between cost and confirmation speed.
                        properties:
                          min:
                            type: number
                            format: float
                            description: >
                              Minimum viable fee for Solana transactions -
                              suitable for non-urgent transactions during low
                              congestion periods

                              (may result in transaction failure during network
                              congestion)
                          low:
                            type: number
                            format: float
                            description: >
                              Low priority fee for Solana transactions -
                              economical option for transactions that aren't
                              time-sensitive

                              (longer confirmation time, lower cost)
                          medium:
                            type: number
                            format: float
                            description: >
                              Medium priority fee for Solana transactions -
                              balanced option for most standard transactions

                              (reasonable confirmation time with moderate cost)
                          high:
                            type: number
                            format: float
                            description: >
                              High priority fee for Solana transactions -
                              prioritized processing for time-sensitive
                              operations

                              like DeFi trades or NFT mints (faster
                              confirmation, higher cost)
                          veryHigh:
                            type: number
                            format: float
                            description: >
                              Very high priority fee for Solana transactions -
                              premium option for critical transactions requiring

                              near-immediate confirmation (very fast
                              confirmation, premium cost)
                          unsafeMax:
                            type: number
                            format: float
                            description: >
                              Maximum observed fee on Solana - the highest fee
                              seen in the analysis window, representing

                              an upper bound during extreme congestion (fastest
                              possible confirmation, but potentially
                              unnecessarily high)
            refIdentifier: '#/components/schemas/PriorityFeeEstimateResponse'
        examples:
          singleEstimateResponse:
            value:
              jsonrpc: '2.0'
              result:
                priorityFeeEstimate: 120000
              id: '1'
          allLevelsResponse:
            value:
              jsonrpc: '2.0'
              result:
                priorityFeeLevels:
                  min: 0
                  low: 2
                  medium: 10082
                  high: 100000
                  veryHigh: 1000000
                  unsafeMax: 50000000
              id: '1'
        description: Successfully retrieved Solana priority fee estimates
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    enum:
                      - '2.0'
                    description: JSON-RPC version identifier
              error:
                allOf:
                  - type: object
                    description: Error information
                    properties:
                      code:
                        type: integer
                        example: -32602
                        description: Error code
                      message:
                        type: string
                        example: Invalid params
                        description: Error message
              id:
                allOf:
                  - type: string
                    description: Client-generated identifier matching the request
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
              id: <string>
        description: Bad request
  deprecated: false
  type: path
components:
  schemas:
    PriorityFeeOptions:
      type: object
      description: >-
        Advanced options for customizing Solana priority fee estimation based on
        network conditions
      properties:
        transactionEncoding:
          type: string
          enum:
            - Base58
            - Base64
          description: Encoding format of the provided Solana transaction
        priorityLevel:
          type: string
          enum:
            - Min
            - Low
            - Medium
            - High
            - VeryHigh
            - UnsafeMax
          description: >-
            Specific priority level to estimate fees for based on current Solana
            network congestion
        includeAllPriorityFeeLevels:
          type: boolean
          description: >-
            When true, returns comprehensive estimates for all priority levels
            from minimum to maximum
        lookbackSlots:
          type: integer
          minimum: 1
          maximum: 150
          description: >-
            Number of recent Solana blockchain slots to analyze for accurate fee
            estimation (1-150)
        includeVote:
          type: boolean
          description: >-
            When true, includes vote transactions in the fee calculation for
            more comprehensive analysis
        recommended:
          type: boolean
          description: >-
            When true, returns the recommended optimal fee based on current
            Solana network conditions
        evaluateEmptySlotAsZero:
          type: boolean
          description: >-
            When true, slots with no transactions are counted as zero-fee slots
            in the calculation

````
# Digital Asset Standard (DAS)

> This page provides an overview of all available Digital Asset Standard (DAS) API endpoints for working with tokens, NFTs, and compressed NFTs on Solana.

<CardGroup cols={2}>
  <Card title="getAsset" href="/api-reference/das/getasset">
    Retrieves information about a specific asset by its ID.
  </Card>

  <Card title="getAssetBatch" href="/api-reference/das/getassetbatch">
    Retrieves information about multiple assets in a single request.
  </Card>

  <Card title="getAssetProof" href="/api-reference/das/getassetproof">
    Fetches a proof for a specific compressed asset.
  </Card>

  <Card title="getAssetProofBatch" href="/api-reference/das/getassetproofbatch">
    Fetches proofs for multiple compressed assets in a single request.
  </Card>

  <Card title="getAssetsByAuthority" href="/api-reference/das/getassetsbyauthority">
    Returns assets by the specified authority address.
  </Card>

  <Card title="getAssetsByCreator" href="/api-reference/das/getassetsbycreator">
    Retrieves assets created by a specific creator address.
  </Card>

  <Card title="getAssetsByGroup" href="/api-reference/das/getassetsbygroup">
    Returns assets that belong to a specific group.
  </Card>

  <Card title="getAssetsByOwner" href="/api-reference/das/getassetsbyowner">
    Lists all assets owned by a specific address.
  </Card>

  <Card title="getNftEditions" href="/api-reference/das/getnfteditions">
    Retrieves information about NFT editions.
  </Card>

  <Card title="getSignaturesForAsset" href="/api-reference/das/getsignaturesforasset">
    Returns transaction signatures involving a specific asset.
  </Card>

  <Card title="getTokenAccounts" href="/api-reference/das/gettokenaccounts">
    Retrieves token accounts based on specified parameters.
  </Card>

  <Card title="searchAssets" href="/api-reference/das/searchassets">
    Searches for assets based on various criteria.
  </Card>
</CardGroup>
# Enhanced Transactions API

> This page provides an overview of all available Enhanced Transactions API endpoints for working with parsed and raw transaction data on Solana.

<CardGroup cols={2}>
  <Card title="Get Parsed Transactions" href="/api-reference/enhanced-transactions/gettransactions">
    Parses and returns enhanced, human-readable versions of the given transactions.
  </Card>

  <Card title="Get Parsed Transactions By Address" href="/api-reference/enhanced-transactions/gettransactionsbyaddress">
    Retrieves enhanced transaction history for a specific address.
  </Card>
</CardGroup>
# Get Enhanced Transactions

> Convert raw Solana transactions into enhanced, human-readable formats with
decoded instruction data and contextual information. Process multiple transactions
in a single request for efficient data analysis and display.


## OpenAPI

````yaml openapi/openapi-definition.yaml post /v0/transactions
paths:
  path: /v0/transactions
  method: post
  servers:
    - url: https://api.helius.xyz
      description: Mainnet RPC endpoint
    - url: https://api-devnet.helius.xyz
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query:
        commitment:
          schema:
            - type: enum<string>
              enum:
                - finalized
                - confirmed
              required: false
              description: >-
                How finalized a block must be to be included in the search. If
                not provided, will default to "finalized" commitment. Note that
                "processed" level commitment is not supported.
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              transactions:
                allOf:
                  - type: array
                    default:
                      - >-
                        4jzQxVTaJ4Fe4Fct9y1aaT9hmVyEjpCqE2bL8JMnuLZbzHZwaL4kZZvNEZ6bEj6fGmiAdCPjmNQHCf8v994PAgDf
                    items:
                      type: string
                      description: The transaction IDs/signatures to parse for.
                    maxItems: 100
            required: true
            refIdentifier: '#/components/schemas/ParseTransactionsRequest'
        examples:
          example:
            value:
              transactions:
                - >-
                  4jzQxVTaJ4Fe4Fct9y1aaT9hmVyEjpCqE2bL8JMnuLZbzHZwaL4kZZvNEZ6bEj6fGmiAdCPjmNQHCf8v994PAgDf
  response:
    '200':
      application/json:
        schemaArray:
          - type: array
            items:
              allOf:
                - $ref: '#/components/schemas/EnhancedTransaction'
        examples:
          example:
            value:
              - description: Human readable interpretation of the transaction
                type: ACCEPT_ESCROW_ARTIST
                source: FORM_FUNCTION
                fee: 5000
                feePayer: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
                signature: >-
                  yy5BT9benHhx8fGCvhcAfTtLEHAtRJ3hRTzVL16bdrTCWm63t2vapfrZQZLJC3RcuagekaXjSs2zUGQvbcto8DK
                slot: *********
                timestamp: **********
                nativeTransfers:
                  - fromUserAccount: <string>
                    toUserAccount: <string>
                    amount: 123
                tokenTransfers:
                  - fromUserAccount: <string>
                    toUserAccount: <string>
                    fromTokenAccount: <string>
                    toTokenAccount: <string>
                    tokenAmount: 123
                    mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                accountData:
                  - account: <string>
                    nativeBalanceChange: 123
                    tokenBalanceChanges:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                transactionError:
                  error: <string>
                instructions:
                  - accounts:
                      - 8uX6yiUuH4UjUb1gMGJAdkXorSuKshqsFGDCFShcK88B
                    data: kdL8HQJrbbvQRGXmoadaja1Qvs
                    programId: MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8
                    innerInstructions:
                      - accounts:
                          - <string>
                        data: <string>
                        programId: <string>
                events:
                  nft:
                    description: <string>
                    type: NFT_BID
                    source: FORM_FUNCTION
                    amount: 1000000
                    fee: 5000
                    feePayer: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
                    signature: >-
                      4jzQxVTaJ4Fe4Fct9y1aaT9hmVyEjpCqE2bL8JMnuLZbzHZwaL4kZZvNEZ6bEj6fGmiAdCPjmNQHCf8v994PAgDf
                    slot: *********
                    timestamp: **********
                    saleType: AUCTION
                    buyer: <string>
                    seller: <string>
                    staker: <string>
                    nfts:
                      - mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        tokenStandard: NonFungible
                  swap:
                    nativeInput:
                      account: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
                      amount: '*********'
                    nativeOutput:
                      account: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
                      amount: '*********'
                    tokenInputs:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                    tokenOutputs:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                    tokenFees:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                    nativeFees:
                      - account: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
                        amount: '*********'
                    innerSwaps:
                      - tokenInputs:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            fromTokenAccount: <string>
                            toTokenAccount: <string>
                            tokenAmount: 123
                            mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        tokenOutputs:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            fromTokenAccount: <string>
                            toTokenAccount: <string>
                            tokenAmount: 123
                            mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        tokenFees:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            fromTokenAccount: <string>
                            toTokenAccount: <string>
                            tokenAmount: 123
                            mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        nativeFees:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            amount: 123
                        programInfo:
                          source: ORCA
                          account: whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc
                          programName: ORCA_WHIRLPOOLS
                          instructionName: whirlpoolSwap
                  compressed:
                    type: CREATE_MERKLE_TREE
                    treeId: <string>
                    assetId: <string>
                    leafIndex: 123
                    instructionIndex: 123
                    innerInstructionIndex: 123
                    newLeafOwner: <string>
                    oldLeafOwner: <string>
                  distributeCompressionRewards:
                    amount: 123
                  setAuthority:
                    account: <string>
                    from: <string>
                    to: <string>
                    instructionIndex: 123
                    innerInstructionIndex: 123
        description: Returns an array of enhanced transactions.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    example: '2.0'
                    description: JSON-RPC version
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: Error code
                        example: -32602
                      message:
                        type: string
                        description: Error message
                        example: Invalid params
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Request identifier
                    example: '1'
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: &ref_3
              - jsonrpc
              - error
              - id
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
              id: '1'
        description: Invalid request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
              id: '1'
        description: Unauthorized request.
    '403':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Forbidden
              id: '1'
        description: Request was forbidden.
    '404':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32601
                message: Method not found
              id: '1'
        description: The specified resource was not found.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Rate limit exceeded
              id: '1'
        description: Exceeded rate limit.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
              id: '1'
        description: >-
          The server encountered an unexpected condition that prevented it from
          fulfilling the request.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
              id: '1'
        description: The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
              id: '1'
        description: The request timed out.
  deprecated: false
  type: path
components:
  schemas:
    TransactionType:
      type: string
      enum:
        - ACCEPT_ESCROW_ARTIST
        - ACCEPT_ESCROW_USER
        - ACCEPT_PROPOSAL
        - ACCEPT_REQUEST_ARTIST
        - ACTIVATE_PROPOSAL
        - ACTIVATE_TRANSACTION
        - ACTIVATE_VAULT
        - ADD_AUTHORITY
        - ADD_BALANCE_LIQUIDITY
        - ADD_BATCH_TRANSACTION
        - ADD_IMBALANCE_LIQUIDITY
        - ADD_INSTRUCTION
        - ADD_ITEM
        - ADD_LIQUIDITY
        - ADD_LIQUIDITY_BY_STRATEGY
        - ADD_LIQUIDITY_BY_STRATEGY_ONE_SIDE
        - ADD_LIQUIDITY_BY_WEIGHT
        - ADD_LIQUIDITY_ONE_SIDE
        - ADD_LIQUIDITY_ONE_SIDE_PRECISE
        - ADD_MEMBER
        - ADD_MEMBER_AND_CHANGE_THRESHOLD
        - ADD_METADATA
        - ADD_PAYMENT_MINT_PAYMENT_METHOD
        - ADD_RARITIES_TO_BANK
        - ADD_REWARDS
        - ADD_SPENDING_LIMIT
        - ADD_TO_POOL
        - ADD_TO_WHITELIST
        - ADD_TOKEN_TO_VAULT
        - ADD_TRAIT_CONFLICTS
        - ADMIN_SYNC_LIQUIDITY
        - APPROVE
        - APPROVE_PROPOSAL
        - APPROVE_TRANSACTION
        - ATTACH_METADATA
        - AUCTION_HOUSE_CREATE
        - AUCTION_MANAGER_CLAIM_BID
        - AUTHORIZE_FUNDER
        - BACKFILL_TOTAL_BLOCKS
        - BEGIN_TRAIT_UPDATE
        - BEGIN_VARIANT_UPDATE
        - BOOTSTRAP_LIQUIDITY
        - BORROW_CNFT_PERPETUAL
        - BORROW_FOX
        - BORROW_OBLIGATION_LIQUIDITY
        - BORROW_PERPETUAL
        - BORROW_SOL_FOR_NFT
        - BORROW_STAKED_BANX_PERPETUAL
        - BOT_CLAIM_SALE
        - BOT_DELIST
        - BOT_LIQUIDATE
        - BOT_LIQUIDATE_SELL
        - BOT_UNFREEZE
        - BOUND_HADO_MARKET_TO_FRAKT_MARKET
        - BURN
        - BURN_NFT
        - BURN_PAYMENT
        - BURN_PAYMENT_TREE
        - BUY_ITEM
        - BUY_LOAN
        - BUY_SUBSCRIPTION
        - BUY_TICKETS
        - CANCEL
        - CANCEL_ALL_AND_PLACE_ORDERS
        - CANCEL_ALL_ORDERS
        - CANCEL_ESCROW
        - CANCEL_LOAN_REQUEST
        - CANCEL_MULTIPLE_ORDERS
        - CANCEL_OFFER
        - CANCEL_ORDER
        - CANCEL_ORDER_BY_CLIENT_ORDER_ID
        - CANCEL_PROPOSAL
        - CANCEL_REWARD
        - CANCEL_SWAP
        - CANCEL_TRANSACTION
        - CANCEL_UP_TO
        - CANCEL_UPDATE
        - CANDY_MACHINE_ROUTE
        - CANDY_MACHINE_UNWRAP
        - CANDY_MACHINE_UPDATE
        - CANDY_MACHINE_WRAP
        - CHANGE_BLOCK_BUILDER
        - CHANGE_COMIC_STATE
        - CHANGE_FEE_RECIPIENT
        - CHANGE_MARKET_STATUS
        - CHANGE_SEAT_STATUS
        - CHANGE_THRESHOLD
        - CHANGE_TIP_RECEIVER
        - CLAIM_AUTHORITY
        - CLAIM_CNFT_PERPETUAL_LOAN
        - CLAIM_FEE
        - CLAIM_NFT
        - CLAIM_NFT_BY_LENDER_CNFT
        - CLAIM_NFT_BY_LENDER_PNFT
        - CLAIM_PERPETUAL_LOAN
        - CLAIM_REWARD
        - CLAIM_REWARDS
        - CLAIM_SALE
        - CLAIM_TIPS
        - CLEAN
        - CLOSE_ACCOUNT
        - CLOSE_BATCH_ACCOUNTS
        - CLOSE_BUNDLED_POSITION
        - CLOSE_CLAIM_STATUS
        - CLOSE_CONFIG
        - CLOSE_CONFIG_TRANSACTION_ACCOUNTS
        - CLOSE_ESCROW_ACCOUNT
        - CLOSE_ITEM
        - CLOSE_MARKET
        - CLOSE_OPEN_ORDERS_ACCOUNT
        - CLOSE_OPEN_ORDERS_INDEXER
        - CLOSE_ORDER
        - CLOSE_POOL
        - CLOSE_POSITION
        - CLOSE_PRESET_PARAMETER
        - CLOSE_TIP_DISTRIBUTION_ACCOUNT
        - CLOSE_VAULT_BATCH_TRANSACTION_ACCOUNT
        - CLOSE_VAULT_TRANSACTION_ACCOUNTS
        - COLLECT_FEES
        - COLLECT_REWARD
        - COMPRESS_NFT
        - COMPRESSED_NFT_BURN
        - COMPRESSED_NFT_CANCEL_REDEEM
        - COMPRESSED_NFT_DELEGATE
        - COMPRESSED_NFT_MINT
        - COMPRESSED_NFT_REDEEM
        - COMPRESSED_NFT_SET_VERIFY_COLLECTION
        - COMPRESSED_NFT_TRANSFER
        - COMPRESSED_NFT_UNVERIFY_COLLECTION
        - COMPRESSED_NFT_UNVERIFY_CREATOR
        - COMPRESSED_NFT_UPDATE_METADATA
        - COMPRESSED_NFT_VERIFY_COLLECTION
        - COMPRESSED_NFT_VERIFY_CREATOR
        - CONSUME_EVENTS
        - CONSUME_GIVEN_EVENTS
        - COPY_CLUSTER_INFO
        - COPY_GOSSIP_CONTACT_INFO
        - COPY_TIP_DISTRIBUTION_ACCOUNT
        - COPY_VOTE_ACCOUNT
        - CRANK
        - CRANK_EVENT_QUEUE
        - CREATE
        - CREATE_AMM
        - CREATE_APPRAISAL
        - CREATE_AVATAR
        - CREATE_AVATAR_CLASS
        - CREATE_BATCH
        - CREATE_BET
        - CREATE_BOND_AND_SELL_TO_OFFERS
        - CREATE_BOND_AND_SELL_TO_OFFERS_CNFT
        - CREATE_BOND_AND_SELL_TO_OFFERS_FOR_TEST
        - CREATE_BOND_OFFER_STANDARD
        - CREATE_COLLECTION
        - CREATE_CONFIG
        - CREATE_CONFIG_TRANSACTION
        - CREATE_ESCROW
        - CREATE_LOCK_ESCROW
        - CREATE_MARKET
        - CREATE_MASTER_EDITION
        - CREATE_MERKLE_TREE
        - CREATE_MINT_METADATA
        - CREATE_MULTISIG
        - CREATE_OPEN_ORDERS_ACCOUNT
        - CREATE_OPEN_ORDERS_INDEXER
        - CREATE_ORDER
        - CREATE_PAYMENT_METHOD
        - CREATE_PERPETUAL_BOND_OFFER
        - CREATE_POOL
        - CREATE_PROPOSAL
        - CREATE_RAFFLE
        - CREATE_STATS
        - CREATE_STORE
        - CREATE_TOKEN_POOL
        - CREATE_TRAIT
        - CREATE_TRANSACTION
        - CREATE_UNCHECKED
        - CREATE_VAULT_TRANSACTION
        - DEAUTHORIZE_FUNDER
        - DECOMPRESS_NFT
        - DECREASE_LIQUIDITY
        - DELEGATE_MERKLE_TREE
        - DELETE_COLLECTION
        - DELETE_POSITION_BUNDLE
        - DELETE_REFERRER_STATE_AND_SHORT_URL
        - DELETE_TOKEN_BADGE
        - DELIST_ITEM
        - DELIST_NFT
        - DEPOSIT
        - DEPOSIT_FRACTIONAL_POOL
        - DEPOSIT_GEM
        - DEPOSIT_OBLIGATION_COLLATERAL
        - DEPOSIT_RESERVE_LIQUIDITY
        - DEPOSIT_RESERVE_LIQUIDITY_AND_OBLIGATION_COLLATERAL
        - DEPOSIT_SOL_TO_FLASH_LOAN_POOL
        - DEPOSIT_TO_BOND_OFFER_STANDARD
        - DEPOSIT_TO_FARM_VAULT
        - DEPOSIT_TO_REWARDS_VAULT
        - DISTRIBUTE_COMPRESSION_REWARDS
        - EDIT_ORDER
        - EDIT_ORDER_PEGGED
        - EMPTY_PAYMENT_ACCOUNT
        - ENABLE_OR_DISABLE_POOL
        - EQUIP_TRAIT
        - EQUIP_TRAIT_AUTHORITY
        - EVICT_SEAT
        - EXECUTE_BATCH_TRANSACTION
        - EXECUTE_CONFIG_TRANSACTION
        - EXECUTE_INSTRUCTION
        - EXECUTE_LOAN
        - EXECUTE_MORTGAGE
        - EXECUTE_TRANSACTION
        - EXECUTE_VAULT_TRANSACTION
        - EXIT_VALIDATE_AND_SELL_TO_BOND_OFFERS_V2
        - EXPIRE
        - EXTEND_LOAN
        - EXTENSION_EXECUTE
        - FILL_ORDER
        - FINALIZE_PROGRAM_INSTRUCTION
        - FINISH_HADO_MARKET
        - FIX_POOL
        - FLASH_BORROW_RESERVE_LIQUIDITY
        - FLASH_REPAY_RESERVE_LIQUIDITY
        - FORCE_CANCEL_ORDERS
        - FORECLOSE_LOAN
        - FRACTIONALIZE
        - FREEZE
        - FUND_REWARD
        - FUSE
        - GET_POOL_INFO
        - GO_TO_A_BIN
        - HARVEST_REWARD
        - IDL_MISSING_TYPES
        - INCREASE_LIQUIDITY
        - INCREASE_ORACLE_LENGTH
        - INIT_AUCTION_MANAGER_V2
        - INIT_BANK
        - INIT_CLUSTER_HISTORY_ACCOUNT
        - INIT_CONFIG
        - INIT_CONFIG_EXTENSION
        - INIT_CUSTOMIZABLE_PERMISSIONLESS_CONSTANT_PRODUCT_POOL
        - INIT_FARM
        - INIT_FARMER
        - INIT_FARMS_FOR_RESERVE
        - INIT_FEE_TIER
        - INIT_LENDING_MARKET
        - INIT_OBLIGATION
        - INIT_OBLIGATION_FARMS_FOR_RESERVE
        - INIT_PERMISSIONED_POOL
        - INIT_PERMISSIONLESS_CONSTANT_PRODUCT_POOL_WITH_CONFIG
        - INIT_PERMISSIONLESS_CONSTANT_PRODUCT_POOL_WITH_CONFIG_2
        - INIT_PERMISSIONLESS_POOL
        - INIT_PERMISSIONLESS_POOL_WITH_FEE_TIER
        - INIT_POOL
        - INIT_POOL_V2
        - INIT_POSITION_BUNDLE
        - INIT_POSITION_BUNDLE_WITH_METADATA
        - INIT_REFERRER_STATE_AND_SHORT_URL
        - INIT_REFERRER_TOKEN_STATE
        - INIT_RENT
        - INIT_RESERVE
        - INIT_REWARD
        - INIT_REWARD_V2
        - INIT_STAKE
        - INIT_SWAP
        - INIT_TICK_ARRAY
        - INIT_TIP_DISTRIBUTION_ACCOUNT
        - INIT_TOKEN_BADGE
        - INIT_USER_METADATA
        - INIT_VALIDATOR_HISTORY_ACCOUNT
        - INIT_VAULT
        - INITIALIZE
        - INITIALIZE_ACCOUNT
        - INITIALIZE_BIN_ARRAY
        - INITIALIZE_BIN_ARRAY_BITMAP_EXTENSION
        - INITIALIZE_CUSTOMIZABLE_PERMISSIONLESS_LB_PAIR
        - INITIALIZE_FARM
        - INITIALIZE_FARM_DELEGATED
        - INITIALIZE_FLASH_LOAN_POOL
        - INITIALIZE_GLOBAL_CONFIG
        - INITIALIZE_HADO_MARKET
        - INITIALIZE_LB_PAIR
        - INITIALIZE_MARKET
        - INITIALIZE_PERMISSION_LB_PAIR
        - INITIALIZE_POSITION
        - INITIALIZE_POSITION_BY_OPERATOR
        - INITIALIZE_POSITION_PDA
        - INITIALIZE_PRESET_PARAMETER
        - INITIALIZE_REWARD
        - INITIALIZE_USER
        - INSTANT_REFINANCE_PERPETUAL_LOAN
        - KICK_ITEM
        - LEND_FOR_NFT
        - LIMIT_ORDER
        - LIQUIDATE
        - LIQUIDATE_BOND_ON_AUCTION_CNFT
        - LIQUIDATE_BOND_ON_AUCTION_PNFT
        - LIQUIDATE_OBLIGATION_AND_REDEEM_RESERVE_COLLATERAL
        - LIST_ITEM
        - LIST_NFT
        - LOAN
        - LOAN_FOX
        - LOCK
        - LOCK_REWARD
        - LOG
        - MAKE_PERPETUAL_MARKET
        - MAP_BANX_TO_POINTS
        - MERGE_CONDITIONAL_TOKENS
        - MERGE_STAKE
        - MIGRATE_BIN_ARRAY
        - MIGRATE_POSITION
        - MIGRATE_TO_PNFT
        - MINT_TO
        - NAME_SUCCESSOR
        - NFT_AUCTION_CANCELLED
        - NFT_AUCTION_CREATED
        - NFT_AUCTION_UPDATED
        - NFT_BID
        - NFT_BID_CANCELLED
        - NFT_CANCEL_LISTING
        - NFT_GLOBAL_BID
        - NFT_GLOBAL_BID_CANCELLED
        - NFT_LISTING
        - NFT_MINT
        - NFT_MINT_REJECTED
        - NFT_PARTICIPATION_REWARD
        - NFT_RENT_ACTIVATE
        - NFT_RENT_CANCEL_LISTING
        - NFT_RENT_END
        - NFT_RENT_LISTING
        - NFT_RENT_UPDATE_LISTING
        - NFT_SALE
        - OFFER_LOAN
        - OPEN_BUNDLED_POSITION
        - OPEN_POSITION
        - OPEN_POSITION_WITH_METADATA
        - OVERRIDE_CURVE_PARAM
        - PARTNER_CLAIM_FEE
        - PATCH_BROKEN_USER_STAKES
        - PAUSE
        - PAYOUT
        - PLACE_AND_TAKE_PERP_ORDER
        - PLACE_BET
        - PLACE_MULTIPLE_POST_ONLY_ORDERS
        - PLACE_ORDER
        - PLACE_ORDER_PEGGED
        - PLACE_ORDERS
        - PLACE_SOL_BET
        - PLACE_TAKE_ORDER
        - PLATFORM_FEE
        - POOL_CANCEL_PROPOSAL
        - POST_MULTI_PYTH
        - POST_PYTH
        - PROGRAM_CONFIG_INIT
        - PROGRAM_CONFIG_SET_AUTH
        - PROGRAM_CONFIG_SET_CREATION_FEE
        - PROGRAM_CONFIG_SET_TREASURY
        - PROPOSE_LOAN
        - PRUNE_ORDERS
        - REALLOC_CLUSTER_HISTORY_ACCOUNT
        - REALLOC_VALIDATOR_HISTORY_ACCOUNT
        - REBORROW_SOL_FOR_NFT
        - RECORD_RARITY_POINTS
        - REDEEM_CONDITIONAL_TOKENS
        - REDEEM_FEES
        - REDEEM_RESERVE_COLLATERAL
        - REDUCE_ORDER
        - REFILL
        - REFINANCE_FBOND_BY_LENDER
        - REFINANCE_PERPETUAL_LOAN
        - REFINANCE_TO_BOND_OFFERS_V2
        - REFINANCE_TO_BOND_OFFERS_V2_CNFT
        - REFRESH_FARM
        - REFRESH_FARMER
        - REFRESH_OBLIGATION
        - REFRESH_OBLIGATION_FARMS_FOR_RESERVE
        - REFRESH_RESERVE
        - REFRESH_RESERVES_BATCH
        - REFRESH_USER_STATE
        - REJECT_PROPOSAL
        - REJECT_SWAP
        - REJECT_TRANSACTION
        - REMOVE_ALL_LIQUIDITY
        - REMOVE_BALANCE_LIQUIDITY
        - REMOVE_BOND_OFFER_V2
        - REMOVE_FROM_POOL
        - REMOVE_FROM_WHITELIST
        - REMOVE_LIQUIDITY
        - REMOVE_LIQUIDITY_BY_RANGE
        - REMOVE_LIQUIDITY_SINGLE_SIDE
        - REMOVE_MEMBER
        - REMOVE_MEMBER_AND_CHANGE_THRESHOLD
        - REMOVE_PERPETUAL_OFFER
        - REMOVE_SPENDING_LIMIT
        - REMOVE_TRAIT
        - REMOVE_TRAIT_AUTHORITY
        - REPAY
        - REPAY_CNFT_PERPETUAL_LOAN
        - REPAY_COMPRESSED
        - REPAY_FBOND_TO_TRADE_TRANSACTIONS
        - REPAY_FBOND_TO_TRADE_TRANSACTIONS_CNFT
        - REPAY_FLASH_LOAN
        - REPAY_LOAN
        - REPAY_OBLIGATION_LIQUIDITY
        - REPAY_PARTIAL_PERPETUAL_LOAN
        - REPAY_PERPETUAL_LOAN
        - REPAY_STAKED_BANX
        - REPAY_STAKED_BANX_PERPETUAL_LOAN
        - REQUEST_ELEVATION_GROUP
        - REQUEST_LOAN
        - REQUEST_PNFT_MIGRATION
        - REQUEST_SEAT
        - REQUEST_SEAT_AUTHORIZED
        - RESCIND_LOAN
        - REVOKE
        - REWARD_USER_ONCE
        - SELL_LOAN
        - SELL_NFT
        - SELL_STAKED_BANX_TO_OFFERS
        - SET_ACTIVATION_POINT
        - SET_AUTHORITY
        - SET_BANK_FLAGS
        - SET_COLLECT_PROTOCOL_FEES_AUTHORITY
        - SET_CONFIG_AUTH
        - SET_CONFIG_EXTENSION_AUTHORITY
        - SET_DEFAULT_FEE_RATE
        - SET_DEFAULT_PROTOCOL_FEE_RATE
        - SET_DELEGATE
        - SET_FEE_AUTHORITY
        - SET_FEE_RATE
        - SET_MARKET_EXPIRED
        - SET_NEW_ADMIN
        - SET_NEW_ORACLE_AUTHORITY
        - SET_NEW_TIP_DISTRIBUTION_PROGRAM
        - SET_PARAMS
        - SET_POOL_FEES
        - SET_PRE_ACTIVATION_DURATION
        - SET_PRE_ACTIVATION_SWAP_ADDRESS
        - SET_PROTOCOL_FEE_RATE
        - SET_RENT_COLLECTOR
        - SET_REWARD_AUTHORITY
        - SET_REWARD_AUTHORITY_BY_SUPER_AUTHORITY
        - SET_REWARD_EMISSIONS
        - SET_REWARD_EMISSIONS_SUPER_AUTHORITY
        - SET_REWARD_EMISSIONS_V2
        - SET_STAKE_DELEGATED
        - SET_TIME_LOCK
        - SET_TOKEN_BADGE_AUTHORITY
        - SET_VAULT_LOCK
        - SET_WHITELISTED_VAULT
        - SETTLE_CONDITIONAL_VAULT
        - SETTLE_FUNDS
        - SETTLE_FUNDS_EXPIRED
        - SETTLE_PNL
        - SOCIALIZE_LOSS
        - SPLIT_STAKE
        - STAKE
        - STAKE_BANX
        - STAKE_SOL
        - STAKE_TOKEN
        - START_PNFT_MIGRATION
        - STUB_ID_BUILD
        - STUB_ORACLE_CLOSE
        - STUB_ORACLE_CREATE
        - STUB_ORACLE_SET
        - SWAP
        - SWAP_EXACT_OUT
        - SWAP_WITH_PRICE_IMPACT
        - SWEEP_FEES
        - SWITCH_FOX
        - SWITCH_FOX_REQUEST
        - SYNC_LIQUIDITY
        - TAKE_COMPRESSED_LOAN
        - TAKE_FLASH_LOAN
        - TAKE_LOAN
        - TAKE_MORTGAGE
        - TERMINATE_PERPETUAL_LOAN
        - THAW
        - TOGGLE_PAIR_STATUS
        - TOKEN_MINT
        - TOPUP
        - TRANSFER
        - TRANSFER_OWNERSHIP
        - TRANSFER_PAYMENT
        - TRANSFER_PAYMENT_TREE
        - TRANSFER_RECIPIENT
        - UNFREEZE
        - UNKNOWN
        - UNLABELED
        - UNPAUSE
        - UNSTAKE
        - UNSTAKE_BANX
        - UNSTAKE_SOL
        - UNSTAKE_TOKEN
        - UNSUB_OR_HARVEST_WEEKS
        - UNSUB_OR_HARVEST_WEEKS_ENHANCED
        - UPDATE
        - UPDATE_ACTIVATION_POINT
        - UPDATE_BANK_MANAGER
        - UPDATE_BOND_OFFER_STANDARD
        - UPDATE_CLASS_VARIANT_AUTHORITY
        - UPDATE_CLASS_VARIANT_METADATA
        - UPDATE_COLLECTION
        - UPDATE_COLLECTION_OR_CREATOR
        - UPDATE_CONFIG
        - UPDATE_EXTERNAL_PRICE_ACCOUNT
        - UPDATE_FARM
        - UPDATE_FARM_ADMIN
        - UPDATE_FARM_CONFIG
        - UPDATE_FEE_PARAMETERS
        - UPDATE_FEES_AND_REWARDS
        - UPDATE_FLOOR
        - UPDATE_GLOBAL_CONFIG
        - UPDATE_GLOBAL_CONFIG_ADMIN
        - UPDATE_HADO_MARKET_FEE
        - UPDATE_INTEREST_PERPETUAL_MARKET
        - UPDATE_ITEM
        - UPDATE_LENDING_MARKET
        - UPDATE_LENDING_MARKET_OWNER
        - UPDATE_OFFER
        - UPDATE_ORDER
        - UPDATE_PERPETUAL_MARKET
        - UPDATE_PERPETUAL_OFFER
        - UPDATE_POOL
        - UPDATE_POOL_COLLECTIONS
        - UPDATE_POOL_MORTGAGE
        - UPDATE_POOL_STATUS
        - UPDATE_POOL_WHITELIST
        - UPDATE_POSITION_OPERATOR
        - UPDATE_PRICING_V2
        - UPDATE_PRIMARY_SALE_METADATA
        - UPDATE_RAFFLE
        - UPDATE_RECORD_AUTHORITY_DATA
        - UPDATE_RESERVE_CONFIG
        - UPDATE_REWARD_DURATION
        - UPDATE_REWARD_FUNDER
        - UPDATE_STAKE_HISTORY
        - UPDATE_STAKING_SETTINGS
        - UPDATE_STATS
        - UPDATE_TRAIT_VARIANT
        - UPDATE_TRAIT_VARIANT_AUTHORITY
        - UPDATE_TRAIT_VARIANT_METADATA
        - UPDATE_USABLE_AMOUNT
        - UPDATE_VARIANT
        - UPDATE_VAULT_OWNER
        - UPGRADE_FOX
        - UPGRADE_FOX_REQUEST
        - UPGRADE_PROGRAM_INSTRUCTION
        - UPLOAD_MERKLE_ROOT
        - USE_SPENDING_LIMIT
        - VALIDATE_SAFETY_DEPOSIT_BOX_V2
        - VERIFY_PAYMENT_MINT
        - VERIFY_PAYMENT_MINT_TEST
        - VOTE
        - WHITELIST_CREATOR
        - WITHDRAW
        - WITHDRAW_FROM_BOND_OFFER_STANDARD
        - WITHDRAW_FROM_FARM_VAULT
        - WITHDRAW_GEM
        - WITHDRAW_INELIGIBLE_REWARD
        - WITHDRAW_LIQUIDITY
        - WITHDRAW_OBLIGATION_COLLATERAL
        - WITHDRAW_OBLIGATION_COLLATERAL_AND_REDEEM_RESERVE_COLLATERAL
        - WITHDRAW_PROTOCOL_FEE
        - WITHDRAW_PROTOCOL_FEES
        - WITHDRAW_REFERRER_FEES
        - WITHDRAW_REWARD
        - WITHDRAW_REWARDS_FROM_VAULT
        - WITHDRAW_SLASHED_AMOUNT
        - WITHDRAW_SOL_FROM_FLASH_LOAN_POOL
        - WITHDRAW_TREASURY
        - WITHDRAW_UNSTAKED_DEPOSITS
    NFTEventType:
      type: string
      enum:
        - NFT_BID
        - NFT_BID_CANCELLED
        - NFT_GLOBAL_BID
        - NFT_GLOBAL_BID_CANCELLED
        - NFT_LISTING
        - NFT_CANCEL_LISTING
        - NFT_SALE
        - NFT_MINT
        - NFT_MINT_REJECTED
        - NFT_AUCTION_CREATED
        - NFT_AUCTION_UPDATED
        - NFT_AUCTION_CANCELLED
        - NFT_PARTICIPATION_REWARD
        - BURN_NFT
        - NFT_RENT_LISTING
        - NFT_RENT_CANCEL_LISTING
        - NFT_RENT_UPDATE_LISTING
        - NFT_RENT_ACTIVATE
        - NFT_RENT_END
        - ATTACH_METADATA
        - MIGRATE_TO_PNFT
        - CREATE_POOL
    CompressedNFTEventType:
      type: string
      enum:
        - CREATE_MERKLE_TREE
        - COMPRESSED_NFT_MINT
        - COMPRESSED_NFT_TRANSFER
        - COMPRESSED_NFT_REDEEM
        - COMPRESSED_NFT_CANCEL_REDEEM
        - COMPRESSED_NFT_BURN
        - COMPRESSED_NFT_DELEGATE
    TransactionSource:
      type: string
      enum:
        - FORM_FUNCTION
        - EXCHANGE_ART
        - CANDY_MACHINE_V3
        - CANDY_MACHINE_V2
        - CANDY_MACHINE_V1
        - UNKNOWN
        - SOLANART
        - SOLSEA
        - MAGIC_EDEN
        - HOLAPLEX
        - METAPLEX
        - OPENSEA
        - SOLANA_PROGRAM_LIBRARY
        - ANCHOR
        - PHANTOM
        - SYSTEM_PROGRAM
        - STAKE_PROGRAM
        - COINBASE
        - CORAL_CUBE
        - HEDGE
        - LAUNCH_MY_NFT
        - GEM_BANK
        - GEM_FARM
        - DEGODS
        - BSL
        - YAWWW
        - ATADIA
        - DIGITAL_EYES
        - HYPERSPACE
        - TENSOR
        - BIFROST
        - JUPITER
        - MERCURIAL
        - SABER
        - SERUM
        - STEP_FINANCE
        - CROPPER
        - RAYDIUM
        - ALDRIN
        - CREMA
        - LIFINITY
        - CYKURA
        - ORCA
        - MARINADE
        - STEPN
        - SENCHA
        - SAROS
        - ENGLISH_AUCTION
        - FOXY
        - HADESWAP
        - FOXY_STAKING
        - FOXY_RAFFLE
        - FOXY_TOKEN_MARKET
        - FOXY_MISSIONS
        - FOXY_MARMALADE
        - FOXY_COINFLIP
        - FOXY_AUCTION
        - CITRUS
        - ZETA
        - ELIXIR
        - ELIXIR_LAUNCHPAD
        - CARDINAL_RENT
        - CARDINAL_STAKING
        - BPF_LOADER
        - BPF_UPGRADEABLE_LOADER
        - SQUADS
        - SHARKY_FI
        - OPEN_CREATOR_PROTOCOL
        - BUBBLEGUM
        - NOVA
        - D_READER
        - RAINDROPS
        - W_SOL
        - DUST
        - SOLI
        - USDC
        - FLWR
        - HDG
        - MEAN
        - UXD
        - SHDW
        - POLIS
        - ATLAS
        - USH
        - TRTLS
        - RUNNER
        - INVICTUS
    SaleType:
      type: string
      enum:
        - AUCTION
        - INSTANT_SALE
        - OFFER
        - GLOBAL_OFFER
        - MINT
        - UNKNOWN
    Token:
      type: object
      properties:
        mint:
          type: string
          example: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
          description: The mint account of the token.
        tokenStandard:
          $ref: '#/components/schemas/TokenStandard'
    TokenStandard:
      type: string
      enum:
        - NonFungible
        - FungibleAsset
        - Fungible
        - NonFungibleEdition
    TokenTransfer:
      type: object
      properties:
        fromUserAccount:
          type: string
          description: The user account the tokens are sent from.
        toUserAccount:
          type: string
          description: The user account the tokens are sent to.
        fromTokenAccount:
          type: string
          description: The token account the tokens are sent from.
        toTokenAccount:
          type: string
          description: The token account the tokens are sent to.
        tokenAmount:
          type: number
          description: The number of tokens sent.
        mint:
          type: string
          description: The mint account of the token.
          example: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
    NativeTransfer:
      type: object
      properties:
        fromUserAccount:
          type: string
          description: The user account the sol is sent from.
        toUserAccount:
          type: string
          description: The user account the sol is sent to.
        amount:
          type: integer
          description: The amount of sol sent (in lamports).
    EnhancedTransaction:
      type: object
      properties:
        description:
          type: string
          example: Human readable interpretation of the transaction
        type:
          $ref: '#/components/schemas/TransactionType'
        source:
          $ref: '#/components/schemas/TransactionSource'
        fee:
          type: integer
          example: 5000
        feePayer:
          type: string
          example: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
        signature:
          type: string
          example: >-
            yy5BT9benHhx8fGCvhcAfTtLEHAtRJ3hRTzVL16bdrTCWm63t2vapfrZQZLJC3RcuagekaXjSs2zUGQvbcto8DK
        slot:
          type: integer
          example: *********
        timestamp:
          type: integer
          example: **********
        nativeTransfers:
          type: array
          items:
            $ref: '#/components/schemas/NativeTransfer'
        tokenTransfers:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
        accountData:
          type: array
          items:
            $ref: '#/components/schemas/AccountData'
        transactionError:
          type: object
          properties:
            error:
              type: string
        instructions:
          type: array
          items:
            $ref: '#/components/schemas/Instruction'
        events:
          type: object
          properties:
            nft:
              $ref: '#/components/schemas/NFTEvent'
            swap:
              $ref: '#/components/schemas/SwapEvent'
            compressed:
              $ref: '#/components/schemas/CompressedNFTEvent'
            distributeCompressionRewards:
              $ref: '#/components/schemas/DistributeCompressionRewardsEvent'
            setAuthority:
              $ref: '#/components/schemas/SetAuthorityEvent'
          description: >-
            Events associated with this transaction. These provide fine-grained
            information about the transaction. They match the types returned
            from the event APIs.
    Instruction:
      type: object
      description: Individual instruction data in a transaction.
      properties:
        accounts:
          type: array
          description: The accounts used in instruction.
          items:
            type: string
            example: 8uX6yiUuH4UjUb1gMGJAdkXorSuKshqsFGDCFShcK88B
        data:
          type: string
          description: Data passed into the instruction
          example: kdL8HQJrbbvQRGXmoadaja1Qvs
        programId:
          type: string
          description: Program used in instruction
          example: MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8
        innerInstructions:
          type: array
          description: Inner instructions used in instruction
          items:
            $ref: '#/components/schemas/InnerInstruction'
    InnerInstruction:
      type: object
      description: Inner instructions for each instruction
      properties:
        accounts:
          type: array
          items:
            type: string
        data:
          type: string
        programId:
          type: string
    AccountData:
      type: object
      description: >-
        Sol and token transfers involving the account are referenced in this
        object.
      properties:
        account:
          type: string
          description: The account that this data is provided for.
        nativeBalanceChange:
          type: number
          description: Native (SOL) balance change of the account.
        tokenBalanceChanges:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: Token balance changes of the account.
    NFTEvent:
      type: object
      properties:
        description:
          type: string
          description: Human readable interpretation of the transaction
        type:
          $ref: '#/components/schemas/NFTEventType'
        source:
          $ref: '#/components/schemas/TransactionSource'
        amount:
          type: integer
          example: 1000000
          description: The amount of the NFT transaction (in lamports).
        fee:
          type: integer
          example: 5000
        feePayer:
          type: string
          example: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
        signature:
          type: string
          example: >-
            4jzQxVTaJ4Fe4Fct9y1aaT9hmVyEjpCqE2bL8JMnuLZbzHZwaL4kZZvNEZ6bEj6fGmiAdCPjmNQHCf8v994PAgDf
        slot:
          type: integer
          example: *********
        timestamp:
          type: integer
          example: **********
        saleType:
          $ref: '#/components/schemas/SaleType'
        buyer:
          type: string
          description: The buyer of the NFT.
        seller:
          type: string
          description: The seller of the NFT.
        staker:
          type: string
          description: The staker of the NFT.
        nfts:
          type: array
          items:
            $ref: '#/components/schemas/Token'
          description: NFTs that are a part of this NFT event.
    CompressedNFTEvent:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/CompressedNFTEventType'
        treeId:
          type: string
          description: The address of the related merkle tree.
        assetId:
          type: string
          description: The id of the compressed nft.
        leafIndex:
          type: integer
          description: The index of the leaf being appended or modified.
        instructionIndex:
          type: integer
          description: The index of the parsed instruction in the transaction.
        innerInstructionIndex:
          type: integer
          description: The index of the parsed inner instruction in the transaction.
        newLeafOwner:
          type: string
          description: The new owner of the leaf.
        oldLeafOwner:
          type: string
          description: The previous owner of the leaf.
    DistributeCompressionRewardsEvent:
      type: object
      properties:
        amount:
          type: integer
          description: Amount transfered in the DistributeCompressionRewardsV0 instruction.
    SetAuthorityEvent:
      type: object
      properties:
        account:
          type: string
          description: Tthe account whose authority is changing.
        from:
          type: string
          description: Current authority.
        to:
          type: string
          description: New authority.
        instructionIndex:
          type: integer
          description: The index of the parsed instruction in the transaction.
        innerInstructionIndex:
          type: integer
          description: The index of the parsed inner instruction in the transaction.
    SwapEvent:
      type: object
      properties:
        nativeInput:
          $ref: '#/components/schemas/NativeBalanceChange'
          description: The native input to the swap in Lamports.
        nativeOutput:
          $ref: '#/components/schemas/NativeBalanceChange'
          description: The native output of the swap in Lamports.
        tokenInputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: The token inputs to the swap.
        tokenOutputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: The token outputs of the swap.
        tokenFees:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: The token fees paid by an account.
        nativeFees:
          type: array
          items:
            $ref: '#/components/schemas/NativeBalanceChange'
          description: The native fees paid by an account.
        innerSwaps:
          type: array
          items:
            $ref: '#/components/schemas/TokenSwap'
          description: >-
            The inner swaps occurring to make this swap happen. Eg. a swap of
            wSOL <-> USDC may be make of multiple swaps from wSOL <-> DUST, DUST
            <-> USDC
    TokenSwap:
      type: object
      properties:
        tokenInputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
          description: The token inputs of this swap.
        tokenOutputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
          description: The token outputs of this swap.
        tokenFees:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
          description: Fees charged with tokens for this swap.
        nativeFees:
          type: array
          items:
            $ref: '#/components/schemas/NativeTransfer'
          description: Fees charged in SOL for this swap.
        programInfo:
          $ref: '#/components/schemas/ProgramInfo'
          description: Information about the program creating this swap.
    ProgramInfo:
      type: object
      properties:
        source:
          type: string
          example: ORCA
        account:
          type: string
          description: The account of the program
          example: whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc
        programName:
          type: string
          description: The name of the program
          example: ORCA_WHIRLPOOLS
        instructionName:
          type: string
          description: >-
            The name of the instruction creating this swap. It is the value of
            instruction name from the Anchor IDL, if it is available.
          example: whirlpoolSwap
    NativeBalanceChange:
      type: object
      properties:
        account:
          type: string
          description: The account the native balance change is for
          example: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
        amount:
          type: string
          description: The amount of the balance change as a string
          example: '*********'
    TokenBalanceChange:
      type: object
      properties:
        userAccount:
          type: string
          example: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
        tokenAccount:
          type: string
          example: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
        mint:
          type: string
          example: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
        rawTokenAmount:
          $ref: '#/components/schemas/RawTokenAmount'
    RawTokenAmount:
      type: object
      properties:
        tokenAmount:
          type: string
        decimals:
          type: integer

````

# Get Enhanced Transactions By Address

> Access comprehensive transaction history for any Solana address with human-readable
decoded data. Filter by transaction types, sources, and time ranges to get detailed
insights into wallet activity with context and decoded instructions.


## OpenAPI

````yaml openapi/openapi-definition.yaml get /v0/addresses/{address}/transactions
paths:
  path: /v0/addresses/{address}/transactions
  method: get
  servers:
    - url: https://api.helius.xyz
      description: Mainnet RPC endpoint
    - url: https://api-devnet.helius.xyz
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path:
        address:
          schema:
            - type: string
              required: true
              description: The address to query for.
              default: 86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY
              example: 86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY
      query:
        before:
          schema:
            - type: string
              required: false
              description: Start searching backwards from this transaction signature.
        until:
          schema:
            - type: string
              required: false
              description: Search until this transaction signature.
        commitment:
          schema:
            - type: enum<string>
              enum:
                - finalized
                - confirmed
              required: false
              description: >-
                How finalized a block must be to be included in the search. If
                not provided, will default to "finalized" commitment. Note that
                "processed" level commitment is not supported.
        source:
          schema:
            - type: enum<string>
              enum:
                - FORM_FUNCTION
                - EXCHANGE_ART
                - CANDY_MACHINE_V3
                - CANDY_MACHINE_V2
                - CANDY_MACHINE_V1
                - UNKNOWN
                - SOLANART
                - SOLSEA
                - MAGIC_EDEN
                - HOLAPLEX
                - METAPLEX
                - OPENSEA
                - SOLANA_PROGRAM_LIBRARY
                - ANCHOR
                - PHANTOM
                - SYSTEM_PROGRAM
                - STAKE_PROGRAM
                - COINBASE
                - CORAL_CUBE
                - HEDGE
                - LAUNCH_MY_NFT
                - GEM_BANK
                - GEM_FARM
                - DEGODS
                - BSL
                - YAWWW
                - ATADIA
                - DIGITAL_EYES
                - HYPERSPACE
                - TENSOR
                - BIFROST
                - JUPITER
                - MERCURIAL
                - SABER
                - SERUM
                - STEP_FINANCE
                - CROPPER
                - RAYDIUM
                - ALDRIN
                - CREMA
                - LIFINITY
                - CYKURA
                - ORCA
                - MARINADE
                - STEPN
                - SENCHA
                - SAROS
                - ENGLISH_AUCTION
                - FOXY
                - HADESWAP
                - FOXY_STAKING
                - FOXY_RAFFLE
                - FOXY_TOKEN_MARKET
                - FOXY_MISSIONS
                - FOXY_MARMALADE
                - FOXY_COINFLIP
                - FOXY_AUCTION
                - CITRUS
                - ZETA
                - ELIXIR
                - ELIXIR_LAUNCHPAD
                - CARDINAL_RENT
                - CARDINAL_STAKING
                - BPF_LOADER
                - BPF_UPGRADEABLE_LOADER
                - SQUADS
                - SHARKY_FI
                - OPEN_CREATOR_PROTOCOL
                - BUBBLEGUM
                - NOVA
                - D_READER
                - RAINDROPS
                - W_SOL
                - DUST
                - SOLI
                - USDC
                - FLWR
                - HDG
                - MEAN
                - UXD
                - SHDW
                - POLIS
                - ATLAS
                - USH
                - TRTLS
                - RUNNER
                - INVICTUS
              required: false
              description: >-
                The TransactionSource to filter by. For a list of possible
                options, see the Transaction Types section.
        type:
          schema:
            - type: enum<string>
              enum:
                - ACCEPT_ESCROW_ARTIST
                - ACCEPT_ESCROW_USER
                - ACCEPT_PROPOSAL
                - ACCEPT_REQUEST_ARTIST
                - ACTIVATE_PROPOSAL
                - ACTIVATE_TRANSACTION
                - ACTIVATE_VAULT
                - ADD_AUTHORITY
                - ADD_BALANCE_LIQUIDITY
                - ADD_BATCH_TRANSACTION
                - ADD_IMBALANCE_LIQUIDITY
                - ADD_INSTRUCTION
                - ADD_ITEM
                - ADD_LIQUIDITY
                - ADD_LIQUIDITY_BY_STRATEGY
                - ADD_LIQUIDITY_BY_STRATEGY_ONE_SIDE
                - ADD_LIQUIDITY_BY_WEIGHT
                - ADD_LIQUIDITY_ONE_SIDE
                - ADD_LIQUIDITY_ONE_SIDE_PRECISE
                - ADD_MEMBER
                - ADD_MEMBER_AND_CHANGE_THRESHOLD
                - ADD_METADATA
                - ADD_PAYMENT_MINT_PAYMENT_METHOD
                - ADD_RARITIES_TO_BANK
                - ADD_REWARDS
                - ADD_SPENDING_LIMIT
                - ADD_TO_POOL
                - ADD_TO_WHITELIST
                - ADD_TOKEN_TO_VAULT
                - ADD_TRAIT_CONFLICTS
                - ADMIN_SYNC_LIQUIDITY
                - APPROVE
                - APPROVE_PROPOSAL
                - APPROVE_TRANSACTION
                - ATTACH_METADATA
                - AUCTION_HOUSE_CREATE
                - AUCTION_MANAGER_CLAIM_BID
                - AUTHORIZE_FUNDER
                - BACKFILL_TOTAL_BLOCKS
                - BEGIN_TRAIT_UPDATE
                - BEGIN_VARIANT_UPDATE
                - BOOTSTRAP_LIQUIDITY
                - BORROW_CNFT_PERPETUAL
                - BORROW_FOX
                - BORROW_OBLIGATION_LIQUIDITY
                - BORROW_PERPETUAL
                - BORROW_SOL_FOR_NFT
                - BORROW_STAKED_BANX_PERPETUAL
                - BOT_CLAIM_SALE
                - BOT_DELIST
                - BOT_LIQUIDATE
                - BOT_LIQUIDATE_SELL
                - BOT_UNFREEZE
                - BOUND_HADO_MARKET_TO_FRAKT_MARKET
                - BURN
                - BURN_NFT
                - BURN_PAYMENT
                - BURN_PAYMENT_TREE
                - BUY_ITEM
                - BUY_LOAN
                - BUY_SUBSCRIPTION
                - BUY_TICKETS
                - CANCEL
                - CANCEL_ALL_AND_PLACE_ORDERS
                - CANCEL_ALL_ORDERS
                - CANCEL_ESCROW
                - CANCEL_LOAN_REQUEST
                - CANCEL_MULTIPLE_ORDERS
                - CANCEL_OFFER
                - CANCEL_ORDER
                - CANCEL_ORDER_BY_CLIENT_ORDER_ID
                - CANCEL_PROPOSAL
                - CANCEL_REWARD
                - CANCEL_SWAP
                - CANCEL_TRANSACTION
                - CANCEL_UP_TO
                - CANCEL_UPDATE
                - CANDY_MACHINE_ROUTE
                - CANDY_MACHINE_UNWRAP
                - CANDY_MACHINE_UPDATE
                - CANDY_MACHINE_WRAP
                - CHANGE_BLOCK_BUILDER
                - CHANGE_COMIC_STATE
                - CHANGE_FEE_RECIPIENT
                - CHANGE_MARKET_STATUS
                - CHANGE_SEAT_STATUS
                - CHANGE_THRESHOLD
                - CHANGE_TIP_RECEIVER
                - CLAIM_AUTHORITY
                - CLAIM_CNFT_PERPETUAL_LOAN
                - CLAIM_FEE
                - CLAIM_NFT
                - CLAIM_NFT_BY_LENDER_CNFT
                - CLAIM_NFT_BY_LENDER_PNFT
                - CLAIM_PERPETUAL_LOAN
                - CLAIM_REWARD
                - CLAIM_REWARDS
                - CLAIM_SALE
                - CLAIM_TIPS
                - CLEAN
                - CLOSE_ACCOUNT
                - CLOSE_BATCH_ACCOUNTS
                - CLOSE_BUNDLED_POSITION
                - CLOSE_CLAIM_STATUS
                - CLOSE_CONFIG
                - CLOSE_CONFIG_TRANSACTION_ACCOUNTS
                - CLOSE_ESCROW_ACCOUNT
                - CLOSE_ITEM
                - CLOSE_MARKET
                - CLOSE_OPEN_ORDERS_ACCOUNT
                - CLOSE_OPEN_ORDERS_INDEXER
                - CLOSE_ORDER
                - CLOSE_POOL
                - CLOSE_POSITION
                - CLOSE_PRESET_PARAMETER
                - CLOSE_TIP_DISTRIBUTION_ACCOUNT
                - CLOSE_VAULT_BATCH_TRANSACTION_ACCOUNT
                - CLOSE_VAULT_TRANSACTION_ACCOUNTS
                - COLLECT_FEES
                - COLLECT_REWARD
                - COMPRESS_NFT
                - COMPRESSED_NFT_BURN
                - COMPRESSED_NFT_CANCEL_REDEEM
                - COMPRESSED_NFT_DELEGATE
                - COMPRESSED_NFT_MINT
                - COMPRESSED_NFT_REDEEM
                - COMPRESSED_NFT_SET_VERIFY_COLLECTION
                - COMPRESSED_NFT_TRANSFER
                - COMPRESSED_NFT_UNVERIFY_COLLECTION
                - COMPRESSED_NFT_UNVERIFY_CREATOR
                - COMPRESSED_NFT_UPDATE_METADATA
                - COMPRESSED_NFT_VERIFY_COLLECTION
                - COMPRESSED_NFT_VERIFY_CREATOR
                - CONSUME_EVENTS
                - CONSUME_GIVEN_EVENTS
                - COPY_CLUSTER_INFO
                - COPY_GOSSIP_CONTACT_INFO
                - COPY_TIP_DISTRIBUTION_ACCOUNT
                - COPY_VOTE_ACCOUNT
                - CRANK
                - CRANK_EVENT_QUEUE
                - CREATE
                - CREATE_AMM
                - CREATE_APPRAISAL
                - CREATE_AVATAR
                - CREATE_AVATAR_CLASS
                - CREATE_BATCH
                - CREATE_BET
                - CREATE_BOND_AND_SELL_TO_OFFERS
                - CREATE_BOND_AND_SELL_TO_OFFERS_CNFT
                - CREATE_BOND_AND_SELL_TO_OFFERS_FOR_TEST
                - CREATE_BOND_OFFER_STANDARD
                - CREATE_COLLECTION
                - CREATE_CONFIG
                - CREATE_CONFIG_TRANSACTION
                - CREATE_ESCROW
                - CREATE_LOCK_ESCROW
                - CREATE_MARKET
                - CREATE_MASTER_EDITION
                - CREATE_MERKLE_TREE
                - CREATE_MINT_METADATA
                - CREATE_MULTISIG
                - CREATE_OPEN_ORDERS_ACCOUNT
                - CREATE_OPEN_ORDERS_INDEXER
                - CREATE_ORDER
                - CREATE_PAYMENT_METHOD
                - CREATE_PERPETUAL_BOND_OFFER
                - CREATE_POOL
                - CREATE_PROPOSAL
                - CREATE_RAFFLE
                - CREATE_STATS
                - CREATE_STORE
                - CREATE_TOKEN_POOL
                - CREATE_TRAIT
                - CREATE_TRANSACTION
                - CREATE_UNCHECKED
                - CREATE_VAULT_TRANSACTION
                - DEAUTHORIZE_FUNDER
                - DECOMPRESS_NFT
                - DECREASE_LIQUIDITY
                - DELEGATE_MERKLE_TREE
                - DELETE_COLLECTION
                - DELETE_POSITION_BUNDLE
                - DELETE_REFERRER_STATE_AND_SHORT_URL
                - DELETE_TOKEN_BADGE
                - DELIST_ITEM
                - DELIST_NFT
                - DEPOSIT
                - DEPOSIT_FRACTIONAL_POOL
                - DEPOSIT_GEM
                - DEPOSIT_OBLIGATION_COLLATERAL
                - DEPOSIT_RESERVE_LIQUIDITY
                - DEPOSIT_RESERVE_LIQUIDITY_AND_OBLIGATION_COLLATERAL
                - DEPOSIT_SOL_TO_FLASH_LOAN_POOL
                - DEPOSIT_TO_BOND_OFFER_STANDARD
                - DEPOSIT_TO_FARM_VAULT
                - DEPOSIT_TO_REWARDS_VAULT
                - DISTRIBUTE_COMPRESSION_REWARDS
                - EDIT_ORDER
                - EDIT_ORDER_PEGGED
                - EMPTY_PAYMENT_ACCOUNT
                - ENABLE_OR_DISABLE_POOL
                - EQUIP_TRAIT
                - EQUIP_TRAIT_AUTHORITY
                - EVICT_SEAT
                - EXECUTE_BATCH_TRANSACTION
                - EXECUTE_CONFIG_TRANSACTION
                - EXECUTE_INSTRUCTION
                - EXECUTE_LOAN
                - EXECUTE_MORTGAGE
                - EXECUTE_TRANSACTION
                - EXECUTE_VAULT_TRANSACTION
                - EXIT_VALIDATE_AND_SELL_TO_BOND_OFFERS_V2
                - EXPIRE
                - EXTEND_LOAN
                - EXTENSION_EXECUTE
                - FILL_ORDER
                - FINALIZE_PROGRAM_INSTRUCTION
                - FINISH_HADO_MARKET
                - FIX_POOL
                - FLASH_BORROW_RESERVE_LIQUIDITY
                - FLASH_REPAY_RESERVE_LIQUIDITY
                - FORCE_CANCEL_ORDERS
                - FORECLOSE_LOAN
                - FRACTIONALIZE
                - FREEZE
                - FUND_REWARD
                - FUSE
                - GET_POOL_INFO
                - GO_TO_A_BIN
                - HARVEST_REWARD
                - IDL_MISSING_TYPES
                - INCREASE_LIQUIDITY
                - INCREASE_ORACLE_LENGTH
                - INIT_AUCTION_MANAGER_V2
                - INIT_BANK
                - INIT_CLUSTER_HISTORY_ACCOUNT
                - INIT_CONFIG
                - INIT_CONFIG_EXTENSION
                - INIT_CUSTOMIZABLE_PERMISSIONLESS_CONSTANT_PRODUCT_POOL
                - INIT_FARM
                - INIT_FARMER
                - INIT_FARMS_FOR_RESERVE
                - INIT_FEE_TIER
                - INIT_LENDING_MARKET
                - INIT_OBLIGATION
                - INIT_OBLIGATION_FARMS_FOR_RESERVE
                - INIT_PERMISSIONED_POOL
                - INIT_PERMISSIONLESS_CONSTANT_PRODUCT_POOL_WITH_CONFIG
                - INIT_PERMISSIONLESS_CONSTANT_PRODUCT_POOL_WITH_CONFIG_2
                - INIT_PERMISSIONLESS_POOL
                - INIT_PERMISSIONLESS_POOL_WITH_FEE_TIER
                - INIT_POOL
                - INIT_POOL_V2
                - INIT_POSITION_BUNDLE
                - INIT_POSITION_BUNDLE_WITH_METADATA
                - INIT_REFERRER_STATE_AND_SHORT_URL
                - INIT_REFERRER_TOKEN_STATE
                - INIT_RENT
                - INIT_RESERVE
                - INIT_REWARD
                - INIT_REWARD_V2
                - INIT_STAKE
                - INIT_SWAP
                - INIT_TICK_ARRAY
                - INIT_TIP_DISTRIBUTION_ACCOUNT
                - INIT_TOKEN_BADGE
                - INIT_USER_METADATA
                - INIT_VALIDATOR_HISTORY_ACCOUNT
                - INIT_VAULT
                - INITIALIZE
                - INITIALIZE_ACCOUNT
                - INITIALIZE_BIN_ARRAY
                - INITIALIZE_BIN_ARRAY_BITMAP_EXTENSION
                - INITIALIZE_CUSTOMIZABLE_PERMISSIONLESS_LB_PAIR
                - INITIALIZE_FARM
                - INITIALIZE_FARM_DELEGATED
                - INITIALIZE_FLASH_LOAN_POOL
                - INITIALIZE_GLOBAL_CONFIG
                - INITIALIZE_HADO_MARKET
                - INITIALIZE_LB_PAIR
                - INITIALIZE_MARKET
                - INITIALIZE_PERMISSION_LB_PAIR
                - INITIALIZE_POSITION
                - INITIALIZE_POSITION_BY_OPERATOR
                - INITIALIZE_POSITION_PDA
                - INITIALIZE_PRESET_PARAMETER
                - INITIALIZE_REWARD
                - INITIALIZE_USER
                - INSTANT_REFINANCE_PERPETUAL_LOAN
                - KICK_ITEM
                - LEND_FOR_NFT
                - LIMIT_ORDER
                - LIQUIDATE
                - LIQUIDATE_BOND_ON_AUCTION_CNFT
                - LIQUIDATE_BOND_ON_AUCTION_PNFT
                - LIQUIDATE_OBLIGATION_AND_REDEEM_RESERVE_COLLATERAL
                - LIST_ITEM
                - LIST_NFT
                - LOAN
                - LOAN_FOX
                - LOCK
                - LOCK_REWARD
                - LOG
                - MAKE_PERPETUAL_MARKET
                - MAP_BANX_TO_POINTS
                - MERGE_CONDITIONAL_TOKENS
                - MERGE_STAKE
                - MIGRATE_BIN_ARRAY
                - MIGRATE_POSITION
                - MIGRATE_TO_PNFT
                - MINT_TO
                - NAME_SUCCESSOR
                - NFT_AUCTION_CANCELLED
                - NFT_AUCTION_CREATED
                - NFT_AUCTION_UPDATED
                - NFT_BID
                - NFT_BID_CANCELLED
                - NFT_CANCEL_LISTING
                - NFT_GLOBAL_BID
                - NFT_GLOBAL_BID_CANCELLED
                - NFT_LISTING
                - NFT_MINT
                - NFT_MINT_REJECTED
                - NFT_PARTICIPATION_REWARD
                - NFT_RENT_ACTIVATE
                - NFT_RENT_CANCEL_LISTING
                - NFT_RENT_END
                - NFT_RENT_LISTING
                - NFT_RENT_UPDATE_LISTING
                - NFT_SALE
                - OFFER_LOAN
                - OPEN_BUNDLED_POSITION
                - OPEN_POSITION
                - OPEN_POSITION_WITH_METADATA
                - OVERRIDE_CURVE_PARAM
                - PARTNER_CLAIM_FEE
                - PATCH_BROKEN_USER_STAKES
                - PAUSE
                - PAYOUT
                - PLACE_AND_TAKE_PERP_ORDER
                - PLACE_BET
                - PLACE_MULTIPLE_POST_ONLY_ORDERS
                - PLACE_ORDER
                - PLACE_ORDER_PEGGED
                - PLACE_ORDERS
                - PLACE_SOL_BET
                - PLACE_TAKE_ORDER
                - PLATFORM_FEE
                - POOL_CANCEL_PROPOSAL
                - POST_MULTI_PYTH
                - POST_PYTH
                - PROGRAM_CONFIG_INIT
                - PROGRAM_CONFIG_SET_AUTH
                - PROGRAM_CONFIG_SET_CREATION_FEE
                - PROGRAM_CONFIG_SET_TREASURY
                - PROPOSE_LOAN
                - PRUNE_ORDERS
                - REALLOC_CLUSTER_HISTORY_ACCOUNT
                - REALLOC_VALIDATOR_HISTORY_ACCOUNT
                - REBORROW_SOL_FOR_NFT
                - RECORD_RARITY_POINTS
                - REDEEM_CONDITIONAL_TOKENS
                - REDEEM_FEES
                - REDEEM_RESERVE_COLLATERAL
                - REDUCE_ORDER
                - REFILL
                - REFINANCE_FBOND_BY_LENDER
                - REFINANCE_PERPETUAL_LOAN
                - REFINANCE_TO_BOND_OFFERS_V2
                - REFINANCE_TO_BOND_OFFERS_V2_CNFT
                - REFRESH_FARM
                - REFRESH_FARMER
                - REFRESH_OBLIGATION
                - REFRESH_OBLIGATION_FARMS_FOR_RESERVE
                - REFRESH_RESERVE
                - REFRESH_RESERVES_BATCH
                - REFRESH_USER_STATE
                - REJECT_PROPOSAL
                - REJECT_SWAP
                - REJECT_TRANSACTION
                - REMOVE_ALL_LIQUIDITY
                - REMOVE_BALANCE_LIQUIDITY
                - REMOVE_BOND_OFFER_V2
                - REMOVE_FROM_POOL
                - REMOVE_FROM_WHITELIST
                - REMOVE_LIQUIDITY
                - REMOVE_LIQUIDITY_BY_RANGE
                - REMOVE_LIQUIDITY_SINGLE_SIDE
                - REMOVE_MEMBER
                - REMOVE_MEMBER_AND_CHANGE_THRESHOLD
                - REMOVE_PERPETUAL_OFFER
                - REMOVE_SPENDING_LIMIT
                - REMOVE_TRAIT
                - REMOVE_TRAIT_AUTHORITY
                - REPAY
                - REPAY_CNFT_PERPETUAL_LOAN
                - REPAY_COMPRESSED
                - REPAY_FBOND_TO_TRADE_TRANSACTIONS
                - REPAY_FBOND_TO_TRADE_TRANSACTIONS_CNFT
                - REPAY_FLASH_LOAN
                - REPAY_LOAN
                - REPAY_OBLIGATION_LIQUIDITY
                - REPAY_PARTIAL_PERPETUAL_LOAN
                - REPAY_PERPETUAL_LOAN
                - REPAY_STAKED_BANX
                - REPAY_STAKED_BANX_PERPETUAL_LOAN
                - REQUEST_ELEVATION_GROUP
                - REQUEST_LOAN
                - REQUEST_PNFT_MIGRATION
                - REQUEST_SEAT
                - REQUEST_SEAT_AUTHORIZED
                - RESCIND_LOAN
                - REVOKE
                - REWARD_USER_ONCE
                - SELL_LOAN
                - SELL_NFT
                - SELL_STAKED_BANX_TO_OFFERS
                - SET_ACTIVATION_POINT
                - SET_AUTHORITY
                - SET_BANK_FLAGS
                - SET_COLLECT_PROTOCOL_FEES_AUTHORITY
                - SET_CONFIG_AUTH
                - SET_CONFIG_EXTENSION_AUTHORITY
                - SET_DEFAULT_FEE_RATE
                - SET_DEFAULT_PROTOCOL_FEE_RATE
                - SET_DELEGATE
                - SET_FEE_AUTHORITY
                - SET_FEE_RATE
                - SET_MARKET_EXPIRED
                - SET_NEW_ADMIN
                - SET_NEW_ORACLE_AUTHORITY
                - SET_NEW_TIP_DISTRIBUTION_PROGRAM
                - SET_PARAMS
                - SET_POOL_FEES
                - SET_PRE_ACTIVATION_DURATION
                - SET_PRE_ACTIVATION_SWAP_ADDRESS
                - SET_PROTOCOL_FEE_RATE
                - SET_RENT_COLLECTOR
                - SET_REWARD_AUTHORITY
                - SET_REWARD_AUTHORITY_BY_SUPER_AUTHORITY
                - SET_REWARD_EMISSIONS
                - SET_REWARD_EMISSIONS_SUPER_AUTHORITY
                - SET_REWARD_EMISSIONS_V2
                - SET_STAKE_DELEGATED
                - SET_TIME_LOCK
                - SET_TOKEN_BADGE_AUTHORITY
                - SET_VAULT_LOCK
                - SET_WHITELISTED_VAULT
                - SETTLE_CONDITIONAL_VAULT
                - SETTLE_FUNDS
                - SETTLE_FUNDS_EXPIRED
                - SETTLE_PNL
                - SOCIALIZE_LOSS
                - SPLIT_STAKE
                - STAKE
                - STAKE_BANX
                - STAKE_SOL
                - STAKE_TOKEN
                - START_PNFT_MIGRATION
                - STUB_ID_BUILD
                - STUB_ORACLE_CLOSE
                - STUB_ORACLE_CREATE
                - STUB_ORACLE_SET
                - SWAP
                - SWAP_EXACT_OUT
                - SWAP_WITH_PRICE_IMPACT
                - SWEEP_FEES
                - SWITCH_FOX
                - SWITCH_FOX_REQUEST
                - SYNC_LIQUIDITY
                - TAKE_COMPRESSED_LOAN
                - TAKE_FLASH_LOAN
                - TAKE_LOAN
                - TAKE_MORTGAGE
                - TERMINATE_PERPETUAL_LOAN
                - THAW
                - TOGGLE_PAIR_STATUS
                - TOKEN_MINT
                - TOPUP
                - TRANSFER
                - TRANSFER_OWNERSHIP
                - TRANSFER_PAYMENT
                - TRANSFER_PAYMENT_TREE
                - TRANSFER_RECIPIENT
                - UNFREEZE
                - UNKNOWN
                - UNLABELED
                - UNPAUSE
                - UNSTAKE
                - UNSTAKE_BANX
                - UNSTAKE_SOL
                - UNSTAKE_TOKEN
                - UNSUB_OR_HARVEST_WEEKS
                - UNSUB_OR_HARVEST_WEEKS_ENHANCED
                - UPDATE
                - UPDATE_ACTIVATION_POINT
                - UPDATE_BANK_MANAGER
                - UPDATE_BOND_OFFER_STANDARD
                - UPDATE_CLASS_VARIANT_AUTHORITY
                - UPDATE_CLASS_VARIANT_METADATA
                - UPDATE_COLLECTION
                - UPDATE_COLLECTION_OR_CREATOR
                - UPDATE_CONFIG
                - UPDATE_EXTERNAL_PRICE_ACCOUNT
                - UPDATE_FARM
                - UPDATE_FARM_ADMIN
                - UPDATE_FARM_CONFIG
                - UPDATE_FEE_PARAMETERS
                - UPDATE_FEES_AND_REWARDS
                - UPDATE_FLOOR
                - UPDATE_GLOBAL_CONFIG
                - UPDATE_GLOBAL_CONFIG_ADMIN
                - UPDATE_HADO_MARKET_FEE
                - UPDATE_INTEREST_PERPETUAL_MARKET
                - UPDATE_ITEM
                - UPDATE_LENDING_MARKET
                - UPDATE_LENDING_MARKET_OWNER
                - UPDATE_OFFER
                - UPDATE_ORDER
                - UPDATE_PERPETUAL_MARKET
                - UPDATE_PERPETUAL_OFFER
                - UPDATE_POOL
                - UPDATE_POOL_COLLECTIONS
                - UPDATE_POOL_MORTGAGE
                - UPDATE_POOL_STATUS
                - UPDATE_POOL_WHITELIST
                - UPDATE_POSITION_OPERATOR
                - UPDATE_PRICING_V2
                - UPDATE_PRIMARY_SALE_METADATA
                - UPDATE_RAFFLE
                - UPDATE_RECORD_AUTHORITY_DATA
                - UPDATE_RESERVE_CONFIG
                - UPDATE_REWARD_DURATION
                - UPDATE_REWARD_FUNDER
                - UPDATE_STAKE_HISTORY
                - UPDATE_STAKING_SETTINGS
                - UPDATE_STATS
                - UPDATE_TRAIT_VARIANT
                - UPDATE_TRAIT_VARIANT_AUTHORITY
                - UPDATE_TRAIT_VARIANT_METADATA
                - UPDATE_USABLE_AMOUNT
                - UPDATE_VARIANT
                - UPDATE_VAULT_OWNER
                - UPGRADE_FOX
                - UPGRADE_FOX_REQUEST
                - UPGRADE_PROGRAM_INSTRUCTION
                - UPLOAD_MERKLE_ROOT
                - USE_SPENDING_LIMIT
                - VALIDATE_SAFETY_DEPOSIT_BOX_V2
                - VERIFY_PAYMENT_MINT
                - VERIFY_PAYMENT_MINT_TEST
                - VOTE
                - WHITELIST_CREATOR
                - WITHDRAW
                - WITHDRAW_FROM_BOND_OFFER_STANDARD
                - WITHDRAW_FROM_FARM_VAULT
                - WITHDRAW_GEM
                - WITHDRAW_INELIGIBLE_REWARD
                - WITHDRAW_LIQUIDITY
                - WITHDRAW_OBLIGATION_COLLATERAL
                - WITHDRAW_OBLIGATION_COLLATERAL_AND_REDEEM_RESERVE_COLLATERAL
                - WITHDRAW_PROTOCOL_FEE
                - WITHDRAW_PROTOCOL_FEES
                - WITHDRAW_REFERRER_FEES
                - WITHDRAW_REWARD
                - WITHDRAW_REWARDS_FROM_VAULT
                - WITHDRAW_SLASHED_AMOUNT
                - WITHDRAW_SOL_FROM_FLASH_LOAN_POOL
                - WITHDRAW_TREASURY
                - WITHDRAW_UNSTAKED_DEPOSITS
              required: false
              description: >-
                The TransactionType to filter by. For a list of possible
                options, see the Transaction Types section.
        limit:
          schema:
            - type: integer
              required: false
              description: >-
                The number of transactions to retrieve. The value should be
                between 1 and 100.
              maximum: 100
              minimum: 1
      header: {}
      cookie: {}
    body: {}
  response:
    '200':
      application/json:
        schemaArray:
          - type: array
            items:
              allOf:
                - $ref: '#/components/schemas/EnhancedTransaction'
        examples:
          example:
            value:
              - description: Human readable interpretation of the transaction
                type: ACCEPT_ESCROW_ARTIST
                source: FORM_FUNCTION
                fee: 5000
                feePayer: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
                signature: >-
                  yy5BT9benHhx8fGCvhcAfTtLEHAtRJ3hRTzVL16bdrTCWm63t2vapfrZQZLJC3RcuagekaXjSs2zUGQvbcto8DK
                slot: *********
                timestamp: **********
                nativeTransfers:
                  - fromUserAccount: <string>
                    toUserAccount: <string>
                    amount: 123
                tokenTransfers:
                  - fromUserAccount: <string>
                    toUserAccount: <string>
                    fromTokenAccount: <string>
                    toTokenAccount: <string>
                    tokenAmount: 123
                    mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                accountData:
                  - account: <string>
                    nativeBalanceChange: 123
                    tokenBalanceChanges:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                transactionError:
                  error: <string>
                instructions:
                  - accounts:
                      - 8uX6yiUuH4UjUb1gMGJAdkXorSuKshqsFGDCFShcK88B
                    data: kdL8HQJrbbvQRGXmoadaja1Qvs
                    programId: MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8
                    innerInstructions:
                      - accounts:
                          - <string>
                        data: <string>
                        programId: <string>
                events:
                  nft:
                    description: <string>
                    type: NFT_BID
                    source: FORM_FUNCTION
                    amount: 1000000
                    fee: 5000
                    feePayer: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
                    signature: >-
                      4jzQxVTaJ4Fe4Fct9y1aaT9hmVyEjpCqE2bL8JMnuLZbzHZwaL4kZZvNEZ6bEj6fGmiAdCPjmNQHCf8v994PAgDf
                    slot: *********
                    timestamp: **********
                    saleType: AUCTION
                    buyer: <string>
                    seller: <string>
                    staker: <string>
                    nfts:
                      - mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        tokenStandard: NonFungible
                  swap:
                    nativeInput:
                      account: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
                      amount: '*********'
                    nativeOutput:
                      account: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
                      amount: '*********'
                    tokenInputs:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                    tokenOutputs:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                    tokenFees:
                      - userAccount: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
                        tokenAccount: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
                        mint: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
                        rawTokenAmount:
                          tokenAmount: <string>
                          decimals: 123
                    nativeFees:
                      - account: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
                        amount: '*********'
                    innerSwaps:
                      - tokenInputs:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            fromTokenAccount: <string>
                            toTokenAccount: <string>
                            tokenAmount: 123
                            mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        tokenOutputs:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            fromTokenAccount: <string>
                            toTokenAccount: <string>
                            tokenAmount: 123
                            mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        tokenFees:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            fromTokenAccount: <string>
                            toTokenAccount: <string>
                            tokenAmount: 123
                            mint: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
                        nativeFees:
                          - fromUserAccount: <string>
                            toUserAccount: <string>
                            amount: 123
                        programInfo:
                          source: ORCA
                          account: whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc
                          programName: ORCA_WHIRLPOOLS
                          instructionName: whirlpoolSwap
                  compressed:
                    type: CREATE_MERKLE_TREE
                    treeId: <string>
                    assetId: <string>
                    leafIndex: 123
                    instructionIndex: 123
                    innerInstructionIndex: 123
                    newLeafOwner: <string>
                    oldLeafOwner: <string>
                  distributeCompressionRewards:
                    amount: 123
                  setAuthority:
                    account: <string>
                    from: <string>
                    to: <string>
                    instructionIndex: 123
                    innerInstructionIndex: 123
        description: Returns an array of enhanced transactions.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    example: '2.0'
                    description: JSON-RPC version
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: Error code
                        example: -32602
                      message:
                        type: string
                        description: Error message
                        example: Invalid params
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Request identifier
                    example: '1'
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: &ref_3
              - jsonrpc
              - error
              - id
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
              id: '1'
        description: Invalid request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
              id: '1'
        description: Unauthorized request.
    '403':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Forbidden
              id: '1'
        description: Request was forbidden.
    '404':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32601
                message: Method not found
              id: '1'
        description: The specified resource was not found.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Rate limit exceeded
              id: '1'
        description: Exceeded rate limit.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
              id: '1'
        description: >-
          The server encountered an unexpected condition that prevented it from
          fulfilling the request.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
              id: '1'
        description: The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            description: JSON-RPC error response format
            refIdentifier: '#/components/schemas/ErrorResponse'
            requiredProperties: *ref_3
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
              id: '1'
        description: The request timed out.
  deprecated: false
  type: path
components:
  schemas:
    TransactionType:
      type: string
      enum:
        - ACCEPT_ESCROW_ARTIST
        - ACCEPT_ESCROW_USER
        - ACCEPT_PROPOSAL
        - ACCEPT_REQUEST_ARTIST
        - ACTIVATE_PROPOSAL
        - ACTIVATE_TRANSACTION
        - ACTIVATE_VAULT
        - ADD_AUTHORITY
        - ADD_BALANCE_LIQUIDITY
        - ADD_BATCH_TRANSACTION
        - ADD_IMBALANCE_LIQUIDITY
        - ADD_INSTRUCTION
        - ADD_ITEM
        - ADD_LIQUIDITY
        - ADD_LIQUIDITY_BY_STRATEGY
        - ADD_LIQUIDITY_BY_STRATEGY_ONE_SIDE
        - ADD_LIQUIDITY_BY_WEIGHT
        - ADD_LIQUIDITY_ONE_SIDE
        - ADD_LIQUIDITY_ONE_SIDE_PRECISE
        - ADD_MEMBER
        - ADD_MEMBER_AND_CHANGE_THRESHOLD
        - ADD_METADATA
        - ADD_PAYMENT_MINT_PAYMENT_METHOD
        - ADD_RARITIES_TO_BANK
        - ADD_REWARDS
        - ADD_SPENDING_LIMIT
        - ADD_TO_POOL
        - ADD_TO_WHITELIST
        - ADD_TOKEN_TO_VAULT
        - ADD_TRAIT_CONFLICTS
        - ADMIN_SYNC_LIQUIDITY
        - APPROVE
        - APPROVE_PROPOSAL
        - APPROVE_TRANSACTION
        - ATTACH_METADATA
        - AUCTION_HOUSE_CREATE
        - AUCTION_MANAGER_CLAIM_BID
        - AUTHORIZE_FUNDER
        - BACKFILL_TOTAL_BLOCKS
        - BEGIN_TRAIT_UPDATE
        - BEGIN_VARIANT_UPDATE
        - BOOTSTRAP_LIQUIDITY
        - BORROW_CNFT_PERPETUAL
        - BORROW_FOX
        - BORROW_OBLIGATION_LIQUIDITY
        - BORROW_PERPETUAL
        - BORROW_SOL_FOR_NFT
        - BORROW_STAKED_BANX_PERPETUAL
        - BOT_CLAIM_SALE
        - BOT_DELIST
        - BOT_LIQUIDATE
        - BOT_LIQUIDATE_SELL
        - BOT_UNFREEZE
        - BOUND_HADO_MARKET_TO_FRAKT_MARKET
        - BURN
        - BURN_NFT
        - BURN_PAYMENT
        - BURN_PAYMENT_TREE
        - BUY_ITEM
        - BUY_LOAN
        - BUY_SUBSCRIPTION
        - BUY_TICKETS
        - CANCEL
        - CANCEL_ALL_AND_PLACE_ORDERS
        - CANCEL_ALL_ORDERS
        - CANCEL_ESCROW
        - CANCEL_LOAN_REQUEST
        - CANCEL_MULTIPLE_ORDERS
        - CANCEL_OFFER
        - CANCEL_ORDER
        - CANCEL_ORDER_BY_CLIENT_ORDER_ID
        - CANCEL_PROPOSAL
        - CANCEL_REWARD
        - CANCEL_SWAP
        - CANCEL_TRANSACTION
        - CANCEL_UP_TO
        - CANCEL_UPDATE
        - CANDY_MACHINE_ROUTE
        - CANDY_MACHINE_UNWRAP
        - CANDY_MACHINE_UPDATE
        - CANDY_MACHINE_WRAP
        - CHANGE_BLOCK_BUILDER
        - CHANGE_COMIC_STATE
        - CHANGE_FEE_RECIPIENT
        - CHANGE_MARKET_STATUS
        - CHANGE_SEAT_STATUS
        - CHANGE_THRESHOLD
        - CHANGE_TIP_RECEIVER
        - CLAIM_AUTHORITY
        - CLAIM_CNFT_PERPETUAL_LOAN
        - CLAIM_FEE
        - CLAIM_NFT
        - CLAIM_NFT_BY_LENDER_CNFT
        - CLAIM_NFT_BY_LENDER_PNFT
        - CLAIM_PERPETUAL_LOAN
        - CLAIM_REWARD
        - CLAIM_REWARDS
        - CLAIM_SALE
        - CLAIM_TIPS
        - CLEAN
        - CLOSE_ACCOUNT
        - CLOSE_BATCH_ACCOUNTS
        - CLOSE_BUNDLED_POSITION
        - CLOSE_CLAIM_STATUS
        - CLOSE_CONFIG
        - CLOSE_CONFIG_TRANSACTION_ACCOUNTS
        - CLOSE_ESCROW_ACCOUNT
        - CLOSE_ITEM
        - CLOSE_MARKET
        - CLOSE_OPEN_ORDERS_ACCOUNT
        - CLOSE_OPEN_ORDERS_INDEXER
        - CLOSE_ORDER
        - CLOSE_POOL
        - CLOSE_POSITION
        - CLOSE_PRESET_PARAMETER
        - CLOSE_TIP_DISTRIBUTION_ACCOUNT
        - CLOSE_VAULT_BATCH_TRANSACTION_ACCOUNT
        - CLOSE_VAULT_TRANSACTION_ACCOUNTS
        - COLLECT_FEES
        - COLLECT_REWARD
        - COMPRESS_NFT
        - COMPRESSED_NFT_BURN
        - COMPRESSED_NFT_CANCEL_REDEEM
        - COMPRESSED_NFT_DELEGATE
        - COMPRESSED_NFT_MINT
        - COMPRESSED_NFT_REDEEM
        - COMPRESSED_NFT_SET_VERIFY_COLLECTION
        - COMPRESSED_NFT_TRANSFER
        - COMPRESSED_NFT_UNVERIFY_COLLECTION
        - COMPRESSED_NFT_UNVERIFY_CREATOR
        - COMPRESSED_NFT_UPDATE_METADATA
        - COMPRESSED_NFT_VERIFY_COLLECTION
        - COMPRESSED_NFT_VERIFY_CREATOR
        - CONSUME_EVENTS
        - CONSUME_GIVEN_EVENTS
        - COPY_CLUSTER_INFO
        - COPY_GOSSIP_CONTACT_INFO
        - COPY_TIP_DISTRIBUTION_ACCOUNT
        - COPY_VOTE_ACCOUNT
        - CRANK
        - CRANK_EVENT_QUEUE
        - CREATE
        - CREATE_AMM
        - CREATE_APPRAISAL
        - CREATE_AVATAR
        - CREATE_AVATAR_CLASS
        - CREATE_BATCH
        - CREATE_BET
        - CREATE_BOND_AND_SELL_TO_OFFERS
        - CREATE_BOND_AND_SELL_TO_OFFERS_CNFT
        - CREATE_BOND_AND_SELL_TO_OFFERS_FOR_TEST
        - CREATE_BOND_OFFER_STANDARD
        - CREATE_COLLECTION
        - CREATE_CONFIG
        - CREATE_CONFIG_TRANSACTION
        - CREATE_ESCROW
        - CREATE_LOCK_ESCROW
        - CREATE_MARKET
        - CREATE_MASTER_EDITION
        - CREATE_MERKLE_TREE
        - CREATE_MINT_METADATA
        - CREATE_MULTISIG
        - CREATE_OPEN_ORDERS_ACCOUNT
        - CREATE_OPEN_ORDERS_INDEXER
        - CREATE_ORDER
        - CREATE_PAYMENT_METHOD
        - CREATE_PERPETUAL_BOND_OFFER
        - CREATE_POOL
        - CREATE_PROPOSAL
        - CREATE_RAFFLE
        - CREATE_STATS
        - CREATE_STORE
        - CREATE_TOKEN_POOL
        - CREATE_TRAIT
        - CREATE_TRANSACTION
        - CREATE_UNCHECKED
        - CREATE_VAULT_TRANSACTION
        - DEAUTHORIZE_FUNDER
        - DECOMPRESS_NFT
        - DECREASE_LIQUIDITY
        - DELEGATE_MERKLE_TREE
        - DELETE_COLLECTION
        - DELETE_POSITION_BUNDLE
        - DELETE_REFERRER_STATE_AND_SHORT_URL
        - DELETE_TOKEN_BADGE
        - DELIST_ITEM
        - DELIST_NFT
        - DEPOSIT
        - DEPOSIT_FRACTIONAL_POOL
        - DEPOSIT_GEM
        - DEPOSIT_OBLIGATION_COLLATERAL
        - DEPOSIT_RESERVE_LIQUIDITY
        - DEPOSIT_RESERVE_LIQUIDITY_AND_OBLIGATION_COLLATERAL
        - DEPOSIT_SOL_TO_FLASH_LOAN_POOL
        - DEPOSIT_TO_BOND_OFFER_STANDARD
        - DEPOSIT_TO_FARM_VAULT
        - DEPOSIT_TO_REWARDS_VAULT
        - DISTRIBUTE_COMPRESSION_REWARDS
        - EDIT_ORDER
        - EDIT_ORDER_PEGGED
        - EMPTY_PAYMENT_ACCOUNT
        - ENABLE_OR_DISABLE_POOL
        - EQUIP_TRAIT
        - EQUIP_TRAIT_AUTHORITY
        - EVICT_SEAT
        - EXECUTE_BATCH_TRANSACTION
        - EXECUTE_CONFIG_TRANSACTION
        - EXECUTE_INSTRUCTION
        - EXECUTE_LOAN
        - EXECUTE_MORTGAGE
        - EXECUTE_TRANSACTION
        - EXECUTE_VAULT_TRANSACTION
        - EXIT_VALIDATE_AND_SELL_TO_BOND_OFFERS_V2
        - EXPIRE
        - EXTEND_LOAN
        - EXTENSION_EXECUTE
        - FILL_ORDER
        - FINALIZE_PROGRAM_INSTRUCTION
        - FINISH_HADO_MARKET
        - FIX_POOL
        - FLASH_BORROW_RESERVE_LIQUIDITY
        - FLASH_REPAY_RESERVE_LIQUIDITY
        - FORCE_CANCEL_ORDERS
        - FORECLOSE_LOAN
        - FRACTIONALIZE
        - FREEZE
        - FUND_REWARD
        - FUSE
        - GET_POOL_INFO
        - GO_TO_A_BIN
        - HARVEST_REWARD
        - IDL_MISSING_TYPES
        - INCREASE_LIQUIDITY
        - INCREASE_ORACLE_LENGTH
        - INIT_AUCTION_MANAGER_V2
        - INIT_BANK
        - INIT_CLUSTER_HISTORY_ACCOUNT
        - INIT_CONFIG
        - INIT_CONFIG_EXTENSION
        - INIT_CUSTOMIZABLE_PERMISSIONLESS_CONSTANT_PRODUCT_POOL
        - INIT_FARM
        - INIT_FARMER
        - INIT_FARMS_FOR_RESERVE
        - INIT_FEE_TIER
        - INIT_LENDING_MARKET
        - INIT_OBLIGATION
        - INIT_OBLIGATION_FARMS_FOR_RESERVE
        - INIT_PERMISSIONED_POOL
        - INIT_PERMISSIONLESS_CONSTANT_PRODUCT_POOL_WITH_CONFIG
        - INIT_PERMISSIONLESS_CONSTANT_PRODUCT_POOL_WITH_CONFIG_2
        - INIT_PERMISSIONLESS_POOL
        - INIT_PERMISSIONLESS_POOL_WITH_FEE_TIER
        - INIT_POOL
        - INIT_POOL_V2
        - INIT_POSITION_BUNDLE
        - INIT_POSITION_BUNDLE_WITH_METADATA
        - INIT_REFERRER_STATE_AND_SHORT_URL
        - INIT_REFERRER_TOKEN_STATE
        - INIT_RENT
        - INIT_RESERVE
        - INIT_REWARD
        - INIT_REWARD_V2
        - INIT_STAKE
        - INIT_SWAP
        - INIT_TICK_ARRAY
        - INIT_TIP_DISTRIBUTION_ACCOUNT
        - INIT_TOKEN_BADGE
        - INIT_USER_METADATA
        - INIT_VALIDATOR_HISTORY_ACCOUNT
        - INIT_VAULT
        - INITIALIZE
        - INITIALIZE_ACCOUNT
        - INITIALIZE_BIN_ARRAY
        - INITIALIZE_BIN_ARRAY_BITMAP_EXTENSION
        - INITIALIZE_CUSTOMIZABLE_PERMISSIONLESS_LB_PAIR
        - INITIALIZE_FARM
        - INITIALIZE_FARM_DELEGATED
        - INITIALIZE_FLASH_LOAN_POOL
        - INITIALIZE_GLOBAL_CONFIG
        - INITIALIZE_HADO_MARKET
        - INITIALIZE_LB_PAIR
        - INITIALIZE_MARKET
        - INITIALIZE_PERMISSION_LB_PAIR
        - INITIALIZE_POSITION
        - INITIALIZE_POSITION_BY_OPERATOR
        - INITIALIZE_POSITION_PDA
        - INITIALIZE_PRESET_PARAMETER
        - INITIALIZE_REWARD
        - INITIALIZE_USER
        - INSTANT_REFINANCE_PERPETUAL_LOAN
        - KICK_ITEM
        - LEND_FOR_NFT
        - LIMIT_ORDER
        - LIQUIDATE
        - LIQUIDATE_BOND_ON_AUCTION_CNFT
        - LIQUIDATE_BOND_ON_AUCTION_PNFT
        - LIQUIDATE_OBLIGATION_AND_REDEEM_RESERVE_COLLATERAL
        - LIST_ITEM
        - LIST_NFT
        - LOAN
        - LOAN_FOX
        - LOCK
        - LOCK_REWARD
        - LOG
        - MAKE_PERPETUAL_MARKET
        - MAP_BANX_TO_POINTS
        - MERGE_CONDITIONAL_TOKENS
        - MERGE_STAKE
        - MIGRATE_BIN_ARRAY
        - MIGRATE_POSITION
        - MIGRATE_TO_PNFT
        - MINT_TO
        - NAME_SUCCESSOR
        - NFT_AUCTION_CANCELLED
        - NFT_AUCTION_CREATED
        - NFT_AUCTION_UPDATED
        - NFT_BID
        - NFT_BID_CANCELLED
        - NFT_CANCEL_LISTING
        - NFT_GLOBAL_BID
        - NFT_GLOBAL_BID_CANCELLED
        - NFT_LISTING
        - NFT_MINT
        - NFT_MINT_REJECTED
        - NFT_PARTICIPATION_REWARD
        - NFT_RENT_ACTIVATE
        - NFT_RENT_CANCEL_LISTING
        - NFT_RENT_END
        - NFT_RENT_LISTING
        - NFT_RENT_UPDATE_LISTING
        - NFT_SALE
        - OFFER_LOAN
        - OPEN_BUNDLED_POSITION
        - OPEN_POSITION
        - OPEN_POSITION_WITH_METADATA
        - OVERRIDE_CURVE_PARAM
        - PARTNER_CLAIM_FEE
        - PATCH_BROKEN_USER_STAKES
        - PAUSE
        - PAYOUT
        - PLACE_AND_TAKE_PERP_ORDER
        - PLACE_BET
        - PLACE_MULTIPLE_POST_ONLY_ORDERS
        - PLACE_ORDER
        - PLACE_ORDER_PEGGED
        - PLACE_ORDERS
        - PLACE_SOL_BET
        - PLACE_TAKE_ORDER
        - PLATFORM_FEE
        - POOL_CANCEL_PROPOSAL
        - POST_MULTI_PYTH
        - POST_PYTH
        - PROGRAM_CONFIG_INIT
        - PROGRAM_CONFIG_SET_AUTH
        - PROGRAM_CONFIG_SET_CREATION_FEE
        - PROGRAM_CONFIG_SET_TREASURY
        - PROPOSE_LOAN
        - PRUNE_ORDERS
        - REALLOC_CLUSTER_HISTORY_ACCOUNT
        - REALLOC_VALIDATOR_HISTORY_ACCOUNT
        - REBORROW_SOL_FOR_NFT
        - RECORD_RARITY_POINTS
        - REDEEM_CONDITIONAL_TOKENS
        - REDEEM_FEES
        - REDEEM_RESERVE_COLLATERAL
        - REDUCE_ORDER
        - REFILL
        - REFINANCE_FBOND_BY_LENDER
        - REFINANCE_PERPETUAL_LOAN
        - REFINANCE_TO_BOND_OFFERS_V2
        - REFINANCE_TO_BOND_OFFERS_V2_CNFT
        - REFRESH_FARM
        - REFRESH_FARMER
        - REFRESH_OBLIGATION
        - REFRESH_OBLIGATION_FARMS_FOR_RESERVE
        - REFRESH_RESERVE
        - REFRESH_RESERVES_BATCH
        - REFRESH_USER_STATE
        - REJECT_PROPOSAL
        - REJECT_SWAP
        - REJECT_TRANSACTION
        - REMOVE_ALL_LIQUIDITY
        - REMOVE_BALANCE_LIQUIDITY
        - REMOVE_BOND_OFFER_V2
        - REMOVE_FROM_POOL
        - REMOVE_FROM_WHITELIST
        - REMOVE_LIQUIDITY
        - REMOVE_LIQUIDITY_BY_RANGE
        - REMOVE_LIQUIDITY_SINGLE_SIDE
        - REMOVE_MEMBER
        - REMOVE_MEMBER_AND_CHANGE_THRESHOLD
        - REMOVE_PERPETUAL_OFFER
        - REMOVE_SPENDING_LIMIT
        - REMOVE_TRAIT
        - REMOVE_TRAIT_AUTHORITY
        - REPAY
        - REPAY_CNFT_PERPETUAL_LOAN
        - REPAY_COMPRESSED
        - REPAY_FBOND_TO_TRADE_TRANSACTIONS
        - REPAY_FBOND_TO_TRADE_TRANSACTIONS_CNFT
        - REPAY_FLASH_LOAN
        - REPAY_LOAN
        - REPAY_OBLIGATION_LIQUIDITY
        - REPAY_PARTIAL_PERPETUAL_LOAN
        - REPAY_PERPETUAL_LOAN
        - REPAY_STAKED_BANX
        - REPAY_STAKED_BANX_PERPETUAL_LOAN
        - REQUEST_ELEVATION_GROUP
        - REQUEST_LOAN
        - REQUEST_PNFT_MIGRATION
        - REQUEST_SEAT
        - REQUEST_SEAT_AUTHORIZED
        - RESCIND_LOAN
        - REVOKE
        - REWARD_USER_ONCE
        - SELL_LOAN
        - SELL_NFT
        - SELL_STAKED_BANX_TO_OFFERS
        - SET_ACTIVATION_POINT
        - SET_AUTHORITY
        - SET_BANK_FLAGS
        - SET_COLLECT_PROTOCOL_FEES_AUTHORITY
        - SET_CONFIG_AUTH
        - SET_CONFIG_EXTENSION_AUTHORITY
        - SET_DEFAULT_FEE_RATE
        - SET_DEFAULT_PROTOCOL_FEE_RATE
        - SET_DELEGATE
        - SET_FEE_AUTHORITY
        - SET_FEE_RATE
        - SET_MARKET_EXPIRED
        - SET_NEW_ADMIN
        - SET_NEW_ORACLE_AUTHORITY
        - SET_NEW_TIP_DISTRIBUTION_PROGRAM
        - SET_PARAMS
        - SET_POOL_FEES
        - SET_PRE_ACTIVATION_DURATION
        - SET_PRE_ACTIVATION_SWAP_ADDRESS
        - SET_PROTOCOL_FEE_RATE
        - SET_RENT_COLLECTOR
        - SET_REWARD_AUTHORITY
        - SET_REWARD_AUTHORITY_BY_SUPER_AUTHORITY
        - SET_REWARD_EMISSIONS
        - SET_REWARD_EMISSIONS_SUPER_AUTHORITY
        - SET_REWARD_EMISSIONS_V2
        - SET_STAKE_DELEGATED
        - SET_TIME_LOCK
        - SET_TOKEN_BADGE_AUTHORITY
        - SET_VAULT_LOCK
        - SET_WHITELISTED_VAULT
        - SETTLE_CONDITIONAL_VAULT
        - SETTLE_FUNDS
        - SETTLE_FUNDS_EXPIRED
        - SETTLE_PNL
        - SOCIALIZE_LOSS
        - SPLIT_STAKE
        - STAKE
        - STAKE_BANX
        - STAKE_SOL
        - STAKE_TOKEN
        - START_PNFT_MIGRATION
        - STUB_ID_BUILD
        - STUB_ORACLE_CLOSE
        - STUB_ORACLE_CREATE
        - STUB_ORACLE_SET
        - SWAP
        - SWAP_EXACT_OUT
        - SWAP_WITH_PRICE_IMPACT
        - SWEEP_FEES
        - SWITCH_FOX
        - SWITCH_FOX_REQUEST
        - SYNC_LIQUIDITY
        - TAKE_COMPRESSED_LOAN
        - TAKE_FLASH_LOAN
        - TAKE_LOAN
        - TAKE_MORTGAGE
        - TERMINATE_PERPETUAL_LOAN
        - THAW
        - TOGGLE_PAIR_STATUS
        - TOKEN_MINT
        - TOPUP
        - TRANSFER
        - TRANSFER_OWNERSHIP
        - TRANSFER_PAYMENT
        - TRANSFER_PAYMENT_TREE
        - TRANSFER_RECIPIENT
        - UNFREEZE
        - UNKNOWN
        - UNLABELED
        - UNPAUSE
        - UNSTAKE
        - UNSTAKE_BANX
        - UNSTAKE_SOL
        - UNSTAKE_TOKEN
        - UNSUB_OR_HARVEST_WEEKS
        - UNSUB_OR_HARVEST_WEEKS_ENHANCED
        - UPDATE
        - UPDATE_ACTIVATION_POINT
        - UPDATE_BANK_MANAGER
        - UPDATE_BOND_OFFER_STANDARD
        - UPDATE_CLASS_VARIANT_AUTHORITY
        - UPDATE_CLASS_VARIANT_METADATA
        - UPDATE_COLLECTION
        - UPDATE_COLLECTION_OR_CREATOR
        - UPDATE_CONFIG
        - UPDATE_EXTERNAL_PRICE_ACCOUNT
        - UPDATE_FARM
        - UPDATE_FARM_ADMIN
        - UPDATE_FARM_CONFIG
        - UPDATE_FEE_PARAMETERS
        - UPDATE_FEES_AND_REWARDS
        - UPDATE_FLOOR
        - UPDATE_GLOBAL_CONFIG
        - UPDATE_GLOBAL_CONFIG_ADMIN
        - UPDATE_HADO_MARKET_FEE
        - UPDATE_INTEREST_PERPETUAL_MARKET
        - UPDATE_ITEM
        - UPDATE_LENDING_MARKET
        - UPDATE_LENDING_MARKET_OWNER
        - UPDATE_OFFER
        - UPDATE_ORDER
        - UPDATE_PERPETUAL_MARKET
        - UPDATE_PERPETUAL_OFFER
        - UPDATE_POOL
        - UPDATE_POOL_COLLECTIONS
        - UPDATE_POOL_MORTGAGE
        - UPDATE_POOL_STATUS
        - UPDATE_POOL_WHITELIST
        - UPDATE_POSITION_OPERATOR
        - UPDATE_PRICING_V2
        - UPDATE_PRIMARY_SALE_METADATA
        - UPDATE_RAFFLE
        - UPDATE_RECORD_AUTHORITY_DATA
        - UPDATE_RESERVE_CONFIG
        - UPDATE_REWARD_DURATION
        - UPDATE_REWARD_FUNDER
        - UPDATE_STAKE_HISTORY
        - UPDATE_STAKING_SETTINGS
        - UPDATE_STATS
        - UPDATE_TRAIT_VARIANT
        - UPDATE_TRAIT_VARIANT_AUTHORITY
        - UPDATE_TRAIT_VARIANT_METADATA
        - UPDATE_USABLE_AMOUNT
        - UPDATE_VARIANT
        - UPDATE_VAULT_OWNER
        - UPGRADE_FOX
        - UPGRADE_FOX_REQUEST
        - UPGRADE_PROGRAM_INSTRUCTION
        - UPLOAD_MERKLE_ROOT
        - USE_SPENDING_LIMIT
        - VALIDATE_SAFETY_DEPOSIT_BOX_V2
        - VERIFY_PAYMENT_MINT
        - VERIFY_PAYMENT_MINT_TEST
        - VOTE
        - WHITELIST_CREATOR
        - WITHDRAW
        - WITHDRAW_FROM_BOND_OFFER_STANDARD
        - WITHDRAW_FROM_FARM_VAULT
        - WITHDRAW_GEM
        - WITHDRAW_INELIGIBLE_REWARD
        - WITHDRAW_LIQUIDITY
        - WITHDRAW_OBLIGATION_COLLATERAL
        - WITHDRAW_OBLIGATION_COLLATERAL_AND_REDEEM_RESERVE_COLLATERAL
        - WITHDRAW_PROTOCOL_FEE
        - WITHDRAW_PROTOCOL_FEES
        - WITHDRAW_REFERRER_FEES
        - WITHDRAW_REWARD
        - WITHDRAW_REWARDS_FROM_VAULT
        - WITHDRAW_SLASHED_AMOUNT
        - WITHDRAW_SOL_FROM_FLASH_LOAN_POOL
        - WITHDRAW_TREASURY
        - WITHDRAW_UNSTAKED_DEPOSITS
    NFTEventType:
      type: string
      enum:
        - NFT_BID
        - NFT_BID_CANCELLED
        - NFT_GLOBAL_BID
        - NFT_GLOBAL_BID_CANCELLED
        - NFT_LISTING
        - NFT_CANCEL_LISTING
        - NFT_SALE
        - NFT_MINT
        - NFT_MINT_REJECTED
        - NFT_AUCTION_CREATED
        - NFT_AUCTION_UPDATED
        - NFT_AUCTION_CANCELLED
        - NFT_PARTICIPATION_REWARD
        - BURN_NFT
        - NFT_RENT_LISTING
        - NFT_RENT_CANCEL_LISTING
        - NFT_RENT_UPDATE_LISTING
        - NFT_RENT_ACTIVATE
        - NFT_RENT_END
        - ATTACH_METADATA
        - MIGRATE_TO_PNFT
        - CREATE_POOL
    CompressedNFTEventType:
      type: string
      enum:
        - CREATE_MERKLE_TREE
        - COMPRESSED_NFT_MINT
        - COMPRESSED_NFT_TRANSFER
        - COMPRESSED_NFT_REDEEM
        - COMPRESSED_NFT_CANCEL_REDEEM
        - COMPRESSED_NFT_BURN
        - COMPRESSED_NFT_DELEGATE
    TransactionSource:
      type: string
      enum:
        - FORM_FUNCTION
        - EXCHANGE_ART
        - CANDY_MACHINE_V3
        - CANDY_MACHINE_V2
        - CANDY_MACHINE_V1
        - UNKNOWN
        - SOLANART
        - SOLSEA
        - MAGIC_EDEN
        - HOLAPLEX
        - METAPLEX
        - OPENSEA
        - SOLANA_PROGRAM_LIBRARY
        - ANCHOR
        - PHANTOM
        - SYSTEM_PROGRAM
        - STAKE_PROGRAM
        - COINBASE
        - CORAL_CUBE
        - HEDGE
        - LAUNCH_MY_NFT
        - GEM_BANK
        - GEM_FARM
        - DEGODS
        - BSL
        - YAWWW
        - ATADIA
        - DIGITAL_EYES
        - HYPERSPACE
        - TENSOR
        - BIFROST
        - JUPITER
        - MERCURIAL
        - SABER
        - SERUM
        - STEP_FINANCE
        - CROPPER
        - RAYDIUM
        - ALDRIN
        - CREMA
        - LIFINITY
        - CYKURA
        - ORCA
        - MARINADE
        - STEPN
        - SENCHA
        - SAROS
        - ENGLISH_AUCTION
        - FOXY
        - HADESWAP
        - FOXY_STAKING
        - FOXY_RAFFLE
        - FOXY_TOKEN_MARKET
        - FOXY_MISSIONS
        - FOXY_MARMALADE
        - FOXY_COINFLIP
        - FOXY_AUCTION
        - CITRUS
        - ZETA
        - ELIXIR
        - ELIXIR_LAUNCHPAD
        - CARDINAL_RENT
        - CARDINAL_STAKING
        - BPF_LOADER
        - BPF_UPGRADEABLE_LOADER
        - SQUADS
        - SHARKY_FI
        - OPEN_CREATOR_PROTOCOL
        - BUBBLEGUM
        - NOVA
        - D_READER
        - RAINDROPS
        - W_SOL
        - DUST
        - SOLI
        - USDC
        - FLWR
        - HDG
        - MEAN
        - UXD
        - SHDW
        - POLIS
        - ATLAS
        - USH
        - TRTLS
        - RUNNER
        - INVICTUS
    SaleType:
      type: string
      enum:
        - AUCTION
        - INSTANT_SALE
        - OFFER
        - GLOBAL_OFFER
        - MINT
        - UNKNOWN
    Token:
      type: object
      properties:
        mint:
          type: string
          example: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
          description: The mint account of the token.
        tokenStandard:
          $ref: '#/components/schemas/TokenStandard'
    TokenStandard:
      type: string
      enum:
        - NonFungible
        - FungibleAsset
        - Fungible
        - NonFungibleEdition
    TokenTransfer:
      type: object
      properties:
        fromUserAccount:
          type: string
          description: The user account the tokens are sent from.
        toUserAccount:
          type: string
          description: The user account the tokens are sent to.
        fromTokenAccount:
          type: string
          description: The token account the tokens are sent from.
        toTokenAccount:
          type: string
          description: The token account the tokens are sent to.
        tokenAmount:
          type: number
          description: The number of tokens sent.
        mint:
          type: string
          description: The mint account of the token.
          example: DsfCsbbPH77p6yeLS1i4ag9UA5gP9xWSvdCx72FJjLsx
    NativeTransfer:
      type: object
      properties:
        fromUserAccount:
          type: string
          description: The user account the sol is sent from.
        toUserAccount:
          type: string
          description: The user account the sol is sent to.
        amount:
          type: integer
          description: The amount of sol sent (in lamports).
    EnhancedTransaction:
      type: object
      properties:
        description:
          type: string
          example: Human readable interpretation of the transaction
        type:
          $ref: '#/components/schemas/TransactionType'
        source:
          $ref: '#/components/schemas/TransactionSource'
        fee:
          type: integer
          example: 5000
        feePayer:
          type: string
          example: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
        signature:
          type: string
          example: >-
            yy5BT9benHhx8fGCvhcAfTtLEHAtRJ3hRTzVL16bdrTCWm63t2vapfrZQZLJC3RcuagekaXjSs2zUGQvbcto8DK
        slot:
          type: integer
          example: *********
        timestamp:
          type: integer
          example: **********
        nativeTransfers:
          type: array
          items:
            $ref: '#/components/schemas/NativeTransfer'
        tokenTransfers:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
        accountData:
          type: array
          items:
            $ref: '#/components/schemas/AccountData'
        transactionError:
          type: object
          properties:
            error:
              type: string
        instructions:
          type: array
          items:
            $ref: '#/components/schemas/Instruction'
        events:
          type: object
          properties:
            nft:
              $ref: '#/components/schemas/NFTEvent'
            swap:
              $ref: '#/components/schemas/SwapEvent'
            compressed:
              $ref: '#/components/schemas/CompressedNFTEvent'
            distributeCompressionRewards:
              $ref: '#/components/schemas/DistributeCompressionRewardsEvent'
            setAuthority:
              $ref: '#/components/schemas/SetAuthorityEvent'
          description: >-
            Events associated with this transaction. These provide fine-grained
            information about the transaction. They match the types returned
            from the event APIs.
    Instruction:
      type: object
      description: Individual instruction data in a transaction.
      properties:
        accounts:
          type: array
          description: The accounts used in instruction.
          items:
            type: string
            example: 8uX6yiUuH4UjUb1gMGJAdkXorSuKshqsFGDCFShcK88B
        data:
          type: string
          description: Data passed into the instruction
          example: kdL8HQJrbbvQRGXmoadaja1Qvs
        programId:
          type: string
          description: Program used in instruction
          example: MEisE1HzehtrDpAAT8PnLHjpSSkRYakotTuJRPjTpo8
        innerInstructions:
          type: array
          description: Inner instructions used in instruction
          items:
            $ref: '#/components/schemas/InnerInstruction'
    InnerInstruction:
      type: object
      description: Inner instructions for each instruction
      properties:
        accounts:
          type: array
          items:
            type: string
        data:
          type: string
        programId:
          type: string
    AccountData:
      type: object
      description: >-
        Sol and token transfers involving the account are referenced in this
        object.
      properties:
        account:
          type: string
          description: The account that this data is provided for.
        nativeBalanceChange:
          type: number
          description: Native (SOL) balance change of the account.
        tokenBalanceChanges:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: Token balance changes of the account.
    NFTEvent:
      type: object
      properties:
        description:
          type: string
          description: Human readable interpretation of the transaction
        type:
          $ref: '#/components/schemas/NFTEventType'
        source:
          $ref: '#/components/schemas/TransactionSource'
        amount:
          type: integer
          example: 1000000
          description: The amount of the NFT transaction (in lamports).
        fee:
          type: integer
          example: 5000
        feePayer:
          type: string
          example: 8cRrU1NzNpjL3k2BwjW3VixAcX6VFc29KHr4KZg8cs2Y
        signature:
          type: string
          example: >-
            4jzQxVTaJ4Fe4Fct9y1aaT9hmVyEjpCqE2bL8JMnuLZbzHZwaL4kZZvNEZ6bEj6fGmiAdCPjmNQHCf8v994PAgDf
        slot:
          type: integer
          example: *********
        timestamp:
          type: integer
          example: **********
        saleType:
          $ref: '#/components/schemas/SaleType'
        buyer:
          type: string
          description: The buyer of the NFT.
        seller:
          type: string
          description: The seller of the NFT.
        staker:
          type: string
          description: The staker of the NFT.
        nfts:
          type: array
          items:
            $ref: '#/components/schemas/Token'
          description: NFTs that are a part of this NFT event.
    CompressedNFTEvent:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/CompressedNFTEventType'
        treeId:
          type: string
          description: The address of the related merkle tree.
        assetId:
          type: string
          description: The id of the compressed nft.
        leafIndex:
          type: integer
          description: The index of the leaf being appended or modified.
        instructionIndex:
          type: integer
          description: The index of the parsed instruction in the transaction.
        innerInstructionIndex:
          type: integer
          description: The index of the parsed inner instruction in the transaction.
        newLeafOwner:
          type: string
          description: The new owner of the leaf.
        oldLeafOwner:
          type: string
          description: The previous owner of the leaf.
    DistributeCompressionRewardsEvent:
      type: object
      properties:
        amount:
          type: integer
          description: Amount transfered in the DistributeCompressionRewardsV0 instruction.
    SetAuthorityEvent:
      type: object
      properties:
        account:
          type: string
          description: Tthe account whose authority is changing.
        from:
          type: string
          description: Current authority.
        to:
          type: string
          description: New authority.
        instructionIndex:
          type: integer
          description: The index of the parsed instruction in the transaction.
        innerInstructionIndex:
          type: integer
          description: The index of the parsed inner instruction in the transaction.
    SwapEvent:
      type: object
      properties:
        nativeInput:
          $ref: '#/components/schemas/NativeBalanceChange'
          description: The native input to the swap in Lamports.
        nativeOutput:
          $ref: '#/components/schemas/NativeBalanceChange'
          description: The native output of the swap in Lamports.
        tokenInputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: The token inputs to the swap.
        tokenOutputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: The token outputs of the swap.
        tokenFees:
          type: array
          items:
            $ref: '#/components/schemas/TokenBalanceChange'
          description: The token fees paid by an account.
        nativeFees:
          type: array
          items:
            $ref: '#/components/schemas/NativeBalanceChange'
          description: The native fees paid by an account.
        innerSwaps:
          type: array
          items:
            $ref: '#/components/schemas/TokenSwap'
          description: >-
            The inner swaps occurring to make this swap happen. Eg. a swap of
            wSOL <-> USDC may be make of multiple swaps from wSOL <-> DUST, DUST
            <-> USDC
    TokenSwap:
      type: object
      properties:
        tokenInputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
          description: The token inputs of this swap.
        tokenOutputs:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
          description: The token outputs of this swap.
        tokenFees:
          type: array
          items:
            $ref: '#/components/schemas/TokenTransfer'
          description: Fees charged with tokens for this swap.
        nativeFees:
          type: array
          items:
            $ref: '#/components/schemas/NativeTransfer'
          description: Fees charged in SOL for this swap.
        programInfo:
          $ref: '#/components/schemas/ProgramInfo'
          description: Information about the program creating this swap.
    ProgramInfo:
      type: object
      properties:
        source:
          type: string
          example: ORCA
        account:
          type: string
          description: The account of the program
          example: whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc
        programName:
          type: string
          description: The name of the program
          example: ORCA_WHIRLPOOLS
        instructionName:
          type: string
          description: >-
            The name of the instruction creating this swap. It is the value of
            instruction name from the Anchor IDL, if it is available.
          example: whirlpoolSwap
    NativeBalanceChange:
      type: object
      properties:
        account:
          type: string
          description: The account the native balance change is for
          example: 2uySTNgvGT2kwqpfgLiSgeBLR3wQyye1i1A2iQWoPiFr
        amount:
          type: string
          description: The amount of the balance change as a string
          example: '*********'
    TokenBalanceChange:
      type: object
      properties:
        userAccount:
          type: string
          example: F54ZGuxyb2gA7vRjzWKLWEMQqCfJxDY1whtqtjdq4CJ
        tokenAccount:
          type: string
          example: 2kvmbRybhrcptDnwyNv6oiFGFEnRVv7MvVyqsxkirgdn
        mint:
          type: string
          example: DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ
        rawTokenAmount:
          $ref: '#/components/schemas/RawTokenAmount'
    RawTokenAmount:
      type: object
      properties:
        tokenAmount:
          type: string
        decimals:
          type: integer

````

# ZK Compression API

> This page provides an overview of all available ZK compression API endpoints for working with compressed data on Solana.

<CardGroup cols={2}>
  <Card title="Get Compressed Account" href="/api-reference/zk-compression/getcompressedaccount">
    Retrieves information about a specific compressed account by its address or hash.
  </Card>

  <Card title="Get Compressed Account Proof" href="/api-reference/zk-compression/getcompressedaccountproof">
    Fetches a proof for a specific compressed account.
  </Card>

  <Card title="Get Compressed Accounts By Owner" href="/api-reference/zk-compression/getcompressedaccountsbyowner">
    Returns all compressed accounts owned by a specific address.
  </Card>

  <Card title="Get Compressed Balance" href="/api-reference/zk-compression/getcompressedbalance">
    Retrieves the balance of a compressed account.
  </Card>

  <Card title="Get Compressed Balance By Owner" href="/api-reference/zk-compression/getcompressedbalancebyowner">
    Gets the total balance of all compressed accounts owned by an address.
  </Card>

  <Card title="Get Compressed Mint Token Holders" href="/api-reference/zk-compression/getcompressedminttokenholders">
    Lists all holders of a specific compressed token mint.
  </Card>

  <Card title="Get Compressed Token Account Balance" href="/api-reference/zk-compression/getcompressedtokenaccountbalance">
    Retrieves the token balance of a compressed token account.
  </Card>

  <Card title="Get Compressed Token Accounts By Delegate" href="/api-reference/zk-compression/getcompressedtokenaccountsbydelegate">
    Returns all compressed token accounts delegated to a specific address.
  </Card>

  <Card title="Get Compressed Token Accounts By Owner" href="/api-reference/zk-compression/getcompressedtokenaccountsbyowner">
    Lists all compressed token accounts owned by a specific address.
  </Card>

  <Card title="Get Compressed Token Balances By Owner" href="/api-reference/zk-compression/getcompressedtokenbalancesbyowner">
    Retrieves all token balances for compressed accounts owned by an address.
  </Card>

  <Card title="Get Compressed Token Balances By Owner V2" href="/api-reference/zk-compression/getcompressedtokenbalancesbyownerv2">
    Enhanced version of the token balances endpoint with additional features.
  </Card>

  <Card title="Get Compression Signatures For Account" href="/api-reference/zk-compression/getcompressionsignaturesforaccount">
    Returns signatures for transactions involving a compressed account.
  </Card>

  <Card title="Get Compression Signatures For Address" href="/api-reference/zk-compression/getcompressionsignaturesforaddress">
    Retrieves signatures for transactions involving a specific address.
  </Card>

  <Card title="Get Compression Signatures For Owner" href="/api-reference/zk-compression/getcompressionsignaturesforowner">
    Returns signatures for transactions where an address is the owner.
  </Card>

  <Card title="Get Compression Signatures For Token Owner" href="/api-reference/zk-compression/getcompressionsignaturesfortokenowner">
    Lists signatures for transactions involving tokens owned by an address.
  </Card>

  <Card title="Get Indexer Health" href="/api-reference/zk-compression/getindexerhealth">
    Returns the health status of the compression indexer.
  </Card>

  <Card title="Get Indexer Slot" href="/api-reference/zk-compression/getindexerslot">
    Retrieves the current slot of the compression indexer.
  </Card>

  <Card title="Get Latest Compression Signatures" href="/api-reference/zk-compression/getlatestcompressionsignatures">
    Returns the most recent transaction signatures related to compression.
  </Card>

  <Card title="Get Latest Non-Voting Signatures" href="/api-reference/zk-compression/getlatestnonvotingsignatures">
    Retrieves recent non-voting transaction signatures.
  </Card>

  <Card title="Get Multiple Compressed Account Proofs" href="/api-reference/zk-compression/getmultiplecompressedaccountproofs">
    Fetches proofs for multiple compressed accounts in a single request.
  </Card>

  <Card title="Get Multiple Compressed Accounts" href="/api-reference/zk-compression/getmultiplecompressedaccounts">
    Retrieves multiple compressed accounts in a single request.
  </Card>

  <Card title="Get Multiple New Address Proofs" href="/api-reference/zk-compression/getmultiplenewaddressproofs">
    Fetches proofs for multiple new addresses.
  </Card>

  <Card title="Get Multiple New Address Proofs V2" href="/api-reference/zk-compression/getmultiplenewaddressProofsv2">
    Enhanced version of the multiple new address proofs endpoint.
  </Card>

  <Card title="Get Transaction With Compression Info" href="/api-reference/zk-compression/gettransactionwithcompressioninfo">
    Returns transaction details with additional compression-related information.
  </Card>

  <Card title="Get Validity Proof" href="/api-reference/zk-compression/getvalidityproof">
    Retrieves a validity proof for compressed data.
  </Card>
</CardGroup>
# getAccountInfo

> Returns all information associated with the account of provided Pubkey.

## OpenAPI

````yaml openapi/rpc-http/getAccountInfo.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    default: '2.0'
              id:
                allOf:
                  - type: string
                    description: A unique identifier for the request.
                    example: '1'
                    default: '1'
              method:
                allOf:
                  - type: string
                    description: The name of the RPC method to invoke.
                    enum:
                      - getAccountInfo
                    default: getAccountInfo
              params:
                allOf:
                  - type: array
                    default:
                      - 83astBRguLMdt2h5U1Tpdq5tjFoJ6noeGwaY3mDLVcri
                    items:
                      oneOf:
                        - title: Pubkey
                          type: string
                          description: >-
                            Pubkey of account to query, as a base-58 encoded
                            string.
                          example: 83astBRguLMdt2h5U1Tpdq5tjFoJ6noeGwaY3mDLVcri
                        - title: Configuration Object
                          type: object
                          description: Configuration object.
                          properties:
                            commitment:
                              type: string
                              description: Commitment level for the query.
                              enum:
                                - finalized
                                - confirmed
                                - processed
                              example: confirmed
                            encoding:
                              type: string
                              description: Encoding format for account data.
                              enum:
                                - base58
                                - base64
                                - base64+zstd
                                - jsonParsed
                              example: base58
                            dataSlice:
                              type: object
                              description: Request a slice of the account's data.
                              properties:
                                offset:
                                  type: integer
                                  description: Byte offset from which to start reading.
                                  example: 0
                                length:
                                  type: integer
                                  description: Number of bytes to return.
                                  example: 64
                            minContextSlot:
                              type: integer
                              description: >-
                                The minimum slot that the request can be
                                evaluated at.
                              example: *********
            required: true
            requiredProperties:
              - jsonrpc
              - id
              - method
              - params
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: 1
              method: getAccountInfo
              params:
                - 83astBRguLMdt2h5U1Tpdq5tjFoJ6noeGwaY3mDLVcri
                - encoding: base58
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    example: '2.0'
              result:
                allOf:
                  - type: object
                    properties:
                      context:
                        type: object
                        description: Context of the request.
                        properties:
                          apiVersion:
                            type: string
                            description: API version of the request.
                            example: 2.0.15
                          slot:
                            type: integer
                            description: Slot number of the response.
                            example: *********
                      value:
                        oneOf:
                          - type: object
                            description: Account data if the account exists.
                            properties:
                              lamports:
                                type: integer
                                description: Number of lamports assigned to this account.
                                example: **************
                              owner:
                                type: string
                                description: >-
                                  Base-58 encoded Pubkey of the program this
                                  account is assigned to.
                                example: '********************************'
                              data:
                                type: array
                                items:
                                  type: string
                                description: >-
                                  Data associated with the account, either as
                                  encoded binary data or JSON format.
                                example:
                                  - ''
                                  - base58
                              executable:
                                type: boolean
                                description: Indicates if the account contains a program.
                                example: false
                              rentEpoch:
                                type: integer
                                description: >-
                                  The epoch at which this account will next owe
                                  rent.
                                example: 18446744073709552000
                              space:
                                type: integer
                                description: The data size of the account.
                                example: 0
              id:
                allOf:
                  - type: integer
                    description: A unique identifier matching the request ID.
                    example: 1
              method:
                allOf:
                  - type: string
                    description: The method being called.
                    example: getAccountInfo
              params:
                allOf:
                  - type: array
                    description: Parameters for the request.
                    items:
                      oneOf:
                        - type: string
                        - type: object
                          properties:
                            encoding:
                              type: string
        examples:
          response:
            value:
              jsonrpc: '2.0'
              result:
                context:
                  apiVersion: 2.0.15
                  slot: *********
                value:
                  lamports: **************
                  owner: '********************************'
                  data:
                    - ''
                    - base58
                  executable: false
                  rentEpoch: 18446744073709552000
                  space: 0
              id: 1
        description: Successfully retrieved account information.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    default: '2.0'
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: Error code.
                      message:
                        type: string
                        description: Error message.
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: A unique identifier for the request.
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
              id: '1'
        description: Bad Request - Invalid request parameters or malformed request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
              id: '1'
        description: Unauthorized - Invalid or missing API key.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Too many requests
              id: '1'
        description: Too Many Requests - Rate limit exceeded.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
              id: '1'
        description: Internal Server Error - An error occurred on the server.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
              id: '1'
        description: Service Unavailable - The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
              id: '1'
        description: Gateway Timeout - The request timed out.
  deprecated: false
  type: path
components:
  schemas: {}

````
# getMultipleAccounts

> Returns the account information for a list of Pubkeys.

## OpenAPI

````yaml openapi/rpc-http/getMultipleAccounts.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
                    default: '2.0'
              id:
                allOf:
                  - type: string
                    description: A unique identifier for the request.
                    example: '1'
                    default: '1'
              method:
                allOf:
                  - type: string
                    description: The name of the RPC method to invoke.
                    enum:
                      - getMultipleAccounts
                    example: getMultipleAccounts
                    default: getMultipleAccounts
              params:
                allOf:
                  - type: array
                    description: Parameters for the method.
                    default:
                      - - vines1vzrYbzLMRdu58ou5XTby4qAqVRLmqo36NKPTg
                        - 4fYNw3dojWmQ4dXtSGE9epjRGy9pFSx62YypT7avPYvA
                    items:
                      oneOf:
                        - type: array
                          description: >-
                            Array of Solana account addresses (up to 100) to
                            fetch in a single optimized batch request.
                          items:
                            type: string
                          example:
                            - vines1vzrYbzLMRdu58ou5XTby4qAqVRLmqo36NKPTg
                            - 4fYNw3dojWmQ4dXtSGE9epjRGy9pFSx62YypT7avPYvA
                        - type: object
                          description: >-
                            Advanced configuration options for customizing the
                            batch account data retrieval.
                          properties:
                            commitment:
                              type: string
                              description: The commitment level for the request.
                              enum:
                                - confirmed
                                - finalized
                                - processed
                              example: finalized
                            minContextSlot:
                              type: integer
                              description: >-
                                The minimum slot that the request can be
                                evaluated at.
                              example: 1000
                            dataSlice:
                              type: object
                              description: Request a slice of the account's data.
                              properties:
                                length:
                                  type: integer
                                  description: Number of bytes to return.
                                  example: 50
                                offset:
                                  type: integer
                                  description: Byte offset from which to start reading.
                                  example: 0
                            encoding:
                              type: string
                              description: Encoding format for the returned account data.
                              enum:
                                - jsonParsed
                                - base58
                                - base64
                                - base64+zstd
                              example: base58
            required: true
            requiredProperties:
              - jsonrpc
              - id
              - method
              - params
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getMultipleAccounts
              params:
                - - vines1vzrYbzLMRdu58ou5XTby4qAqVRLmqo36NKPTg
                  - 4fYNw3dojWmQ4dXtSGE9epjRGy9pFSx62YypT7avPYvA
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              id:
                allOf:
                  - type: string
                    description: Identifier matching the request.
                    example: '1'
              result:
                allOf:
                  - type: object
                    description: Accounts information with context.
                    properties:
                      context:
                        type: object
                        description: Context of the response.
                        properties:
                          apiVersion:
                            type: string
                            description: API version of the response.
                            example: 2.0.15
                          slot:
                            type: integer
                            description: The slot at which the data was fetched.
                            example: *********
                      value:
                        type: array
                        description: Array of account details.
                        items:
                          oneOf:
                            - type: 'null'
                              description: Null if the account doesn't exist.
                            - type: object
                              description: Account details.
                              properties:
                                lamports:
                                  type: integer
                                  description: Number of lamports assigned to the account.
                                  example: **************
                                owner:
                                  type: string
                                  description: >-
                                    Base-58 encoded Pubkey of the program the
                                    account is assigned to.
                                  example: '********************************'
                                data:
                                  type: array
                                  description: >-
                                    Account data as encoded binary or JSON
                                    format.
                                  items:
                                    type: string
                                  example:
                                    - ''
                                    - base58
                                executable:
                                  type: boolean
                                  description: Indicates if the account contains a program.
                                  example: false
                                rentEpoch:
                                  type: integer
                                  description: >-
                                    Epoch at which this account will next owe
                                    rent.
                                  example: 18446744073709552000
                                space:
                                  type: integer
                                  description: Data size of the account.
                                  example: 0
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              result:
                context:
                  apiVersion: 2.0.15
                  slot: *********
                value:
                  - null
        description: Successfully retrieved the account details.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: The error code.
                        example: -32602
                      message:
                        type: string
                        description: The error message.
                      data:
                        type: object
                        description: Additional data about the error.
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Identifier matching the request.
                    example: '1'
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
                data:
                  additionalInfo: >-
                    Please check the request parameters and ensure they are
                    valid.
              id: '1'
        description: Bad Request - Invalid request parameters or malformed request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
                data:
                  additionalInfo: Please verify your API key and try again.
              id: '1'
        description: Unauthorized - Invalid or missing API key.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Too many requests
                data:
                  additionalInfo: >-
                    You have exceeded the allowed number of requests. Please
                    wait and try again later.
              id: '1'
        description: Too Many Requests - Rate limit exceeded.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
                data:
                  additionalInfo: >-
                    An unexpected error occurred on the server. Please try again
                    later or contact support.
              id: '1'
        description: Internal Server Error - An error occurred on the server.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
                data:
                  additionalInfo: >-
                    The service is currently unavailable. Please try again later
                    or contact support.
              id: '1'
        description: Service Unavailable - The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
                data:
                  additionalInfo: >-
                    The request timed out. Please try again later or contact
                    support.
              id: '1'
        description: Gateway Timeout - The request timed out.
  deprecated: false
  type: path
components:
  schemas: {}

````# getProgramAccounts

> Returns all accounts owned by the provided program Pubkey.

## OpenAPI

````yaml openapi/rpc-http/getProgramAccounts.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
                    default: '2.0'
              id:
                allOf:
                  - type: string
                    description: A unique identifier for the request.
                    example: '1'
                    default: '1'
              method:
                allOf:
                  - type: string
                    description: The name of the RPC method to invoke.
                    enum:
                      - getProgramAccounts
                    example: getProgramAccounts
                    default: getProgramAccounts
              params:
                allOf:
                  - type: array
                    description: Parameters for the method.
                    default:
                      - 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                    items:
                      oneOf:
                        - type: string
                          description: >-
                            The Solana program public key (address) to query
                            accounts for, as a base-58 encoded string.
                          example: 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                        - type: object
                          description: >-
                            Advanced configuration options for optimizing
                            program account queries.
                          properties:
                            commitment:
                              type: string
                              description: The commitment level for the request.
                              enum:
                                - confirmed
                                - finalized
                                - processed
                              example: finalized
                            minContextSlot:
                              type: integer
                              description: >-
                                The minimum slot that the request can be
                                evaluated at.
                              example: 1000
                            withContext:
                              type: boolean
                              description: Wrap the result in an RpcResponse JSON object.
                              example: true
                            encoding:
                              type: string
                              description: Encoding format for the returned account data.
                              enum:
                                - jsonParsed
                                - base58
                                - base64
                                - base64+zstd
                              example: jsonParsed
                            dataSlice:
                              type: object
                              description: Request a slice of the account's data.
                              properties:
                                length:
                                  type: integer
                                  description: Number of bytes to return.
                                  example: 50
                                offset:
                                  type: integer
                                  description: Byte offset from which to start reading.
                                  example: 0
                            filters:
                              type: array
                              description: >-
                                Powerful filtering system to efficiently query
                                specific Solana account data patterns.
                              items:
                                oneOf:
                                  - type: object
                                    description: >-
                                      Filter Solana accounts by their exact data
                                      size in bytes.
                                    properties:
                                      dataSize:
                                        type: integer
                                        description: >-
                                          The exact size of the account data in
                                          bytes for filtering.
                                        example: 17
                                  - type: object
                                    description: >-
                                      Filter Solana accounts by comparing data
                                      at specific memory offsets (most powerful
                                      filter).
                                    properties:
                                      memcmp:
                                        type: object
                                        description: >-
                                          Memory comparison filter for finding
                                          accounts with specific data patterns.
                                        properties:
                                          offset:
                                            type: integer
                                            description: >-
                                              Byte offset within account data to
                                              perform the comparison.
                                            example: 4
                                          bytes:
                                            type: string
                                            description: >-
                                              Base-58 encoded data to compare at the
                                              specified offset position.
                                            example: 3Mc6vR
            required: true
            requiredProperties:
              - jsonrpc
              - id
              - method
              - params
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getProgramAccounts
              params:
                - 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              id:
                allOf:
                  - type: string
                    description: Identifier matching the request.
                    example: '1'
              result:
                allOf:
                  - oneOf:
                      - type: array
                        description: List of program accounts.
                        items:
                          type: object
                          properties:
                            pubkey:
                              type: string
                              description: The account Pubkey as a base-58 encoded string.
                              example: CxELquR1gPP8wHe33gZ4QxqGB3sZ9RSwsJ2KshVewkFY
                            account:
                              type: object
                              description: Details about the account.
                              properties:
                                lamports:
                                  type: integer
                                  description: Number of lamports assigned to this account.
                                  example: ********
                                owner:
                                  type: string
                                  description: >-
                                    Base-58 encoded Pubkey of the program this
                                    account is assigned to.
                                  example: 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                                data:
                                  type: array
                                  description: >-
                                    Account data as encoded binary or JSON
                                    format.
                                  items:
                                    type: string
                                  example:
                                    - 2R9jLfiAQ9bgdcw6h8s44439
                                    - base58
                                executable:
                                  type: boolean
                                  description: Indicates if the account contains a program.
                                  example: false
                                rentEpoch:
                                  type: integer
                                  description: >-
                                    The epoch at which this account will next
                                    owe rent.
                                  example: 28
                                space:
                                  type: integer
                                  description: The data size of the account.
                                  example: 42
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              result:
                - pubkey: CxELquR1gPP8wHe33gZ4QxqGB3sZ9RSwsJ2KshVewkFY
                  account:
                    lamports: ********
                    owner: 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                    data:
                      - 2R9jLfiAQ9bgdcw6h8s44439
                      - base58
                    executable: false
                    rentEpoch: 28
                    space: 42
        description: Successfully retrieved program accounts.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: The error code.
                        example: -32602
                      message:
                        type: string
                        description: The error message.
                      data:
                        type: object
                        description: Additional data about the error.
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Identifier matching the request.
                    example: '1'
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
                data: {}
              id: '1'
        description: Bad Request - Invalid request parameters or malformed request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
                data: {}
              id: '1'
        description: Unauthorized - Invalid or missing API key.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Too many requests
                data: {}
              id: '1'
        description: Too Many Requests - Rate limit exceeded.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
                data: {}
              id: '1'
        description: Internal Server Error - An error occurred on the server.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
                data: {}
              id: '1'
        description: Service Unavailable - The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
                data: {}
              id: '1'
        description: Gateway Timeout - The request timed out.
  deprecated: false
  type: path
components:
  schemas: {}

````# getProgramAccounts

> Returns all accounts owned by the provided program Pubkey.

## OpenAPI

````yaml openapi/rpc-http/getProgramAccounts.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
                    default: '2.0'
              id:
                allOf:
                  - type: string
                    description: A unique identifier for the request.
                    example: '1'
                    default: '1'
              method:
                allOf:
                  - type: string
                    description: The name of the RPC method to invoke.
                    enum:
                      - getProgramAccounts
                    example: getProgramAccounts
                    default: getProgramAccounts
              params:
                allOf:
                  - type: array
                    description: Parameters for the method.
                    default:
                      - 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                    items:
                      oneOf:
                        - type: string
                          description: >-
                            The Solana program public key (address) to query
                            accounts for, as a base-58 encoded string.
                          example: 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                        - type: object
                          description: >-
                            Advanced configuration options for optimizing
                            program account queries.
                          properties:
                            commitment:
                              type: string
                              description: The commitment level for the request.
                              enum:
                                - confirmed
                                - finalized
                                - processed
                              example: finalized
                            minContextSlot:
                              type: integer
                              description: >-
                                The minimum slot that the request can be
                                evaluated at.
                              example: 1000
                            withContext:
                              type: boolean
                              description: Wrap the result in an RpcResponse JSON object.
                              example: true
                            encoding:
                              type: string
                              description: Encoding format for the returned account data.
                              enum:
                                - jsonParsed
                                - base58
                                - base64
                                - base64+zstd
                              example: jsonParsed
                            dataSlice:
                              type: object
                              description: Request a slice of the account's data.
                              properties:
                                length:
                                  type: integer
                                  description: Number of bytes to return.
                                  example: 50
                                offset:
                                  type: integer
                                  description: Byte offset from which to start reading.
                                  example: 0
                            filters:
                              type: array
                              description: >-
                                Powerful filtering system to efficiently query
                                specific Solana account data patterns.
                              items:
                                oneOf:
                                  - type: object
                                    description: >-
                                      Filter Solana accounts by their exact data
                                      size in bytes.
                                    properties:
                                      dataSize:
                                        type: integer
                                        description: >-
                                          The exact size of the account data in
                                          bytes for filtering.
                                        example: 17
                                  - type: object
                                    description: >-
                                      Filter Solana accounts by comparing data
                                      at specific memory offsets (most powerful
                                      filter).
                                    properties:
                                      memcmp:
                                        type: object
                                        description: >-
                                          Memory comparison filter for finding
                                          accounts with specific data patterns.
                                        properties:
                                          offset:
                                            type: integer
                                            description: >-
                                              Byte offset within account data to
                                              perform the comparison.
                                            example: 4
                                          bytes:
                                            type: string
                                            description: >-
                                              Base-58 encoded data to compare at the
                                              specified offset position.
                                            example: 3Mc6vR
            required: true
            requiredProperties:
              - jsonrpc
              - id
              - method
              - params
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getProgramAccounts
              params:
                - 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              id:
                allOf:
                  - type: string
                    description: Identifier matching the request.
                    example: '1'
              result:
                allOf:
                  - oneOf:
                      - type: array
                        description: List of program accounts.
                        items:
                          type: object
                          properties:
                            pubkey:
                              type: string
                              description: The account Pubkey as a base-58 encoded string.
                              example: CxELquR1gPP8wHe33gZ4QxqGB3sZ9RSwsJ2KshVewkFY
                            account:
                              type: object
                              description: Details about the account.
                              properties:
                                lamports:
                                  type: integer
                                  description: Number of lamports assigned to this account.
                                  example: ********
                                owner:
                                  type: string
                                  description: >-
                                    Base-58 encoded Pubkey of the program this
                                    account is assigned to.
                                  example: 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                                data:
                                  type: array
                                  description: >-
                                    Account data as encoded binary or JSON
                                    format.
                                  items:
                                    type: string
                                  example:
                                    - 2R9jLfiAQ9bgdcw6h8s44439
                                    - base58
                                executable:
                                  type: boolean
                                  description: Indicates if the account contains a program.
                                  example: false
                                rentEpoch:
                                  type: integer
                                  description: >-
                                    The epoch at which this account will next
                                    owe rent.
                                  example: 28
                                space:
                                  type: integer
                                  description: The data size of the account.
                                  example: 42
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              result:
                - pubkey: CxELquR1gPP8wHe33gZ4QxqGB3sZ9RSwsJ2KshVewkFY
                  account:
                    lamports: ********
                    owner: 4Nd1mBQtrMJVYVfKf2PJy9NZUZdTAsp7D4xWLs4gDB4T
                    data:
                      - 2R9jLfiAQ9bgdcw6h8s44439
                      - base58
                    executable: false
                    rentEpoch: 28
                    space: 42
        description: Successfully retrieved program accounts.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: The error code.
                        example: -32602
                      message:
                        type: string
                        description: The error message.
                      data:
                        type: object
                        description: Additional data about the error.
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Identifier matching the request.
                    example: '1'
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
                data: {}
              id: '1'
        description: Bad Request - Invalid request parameters or malformed request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
                data: {}
              id: '1'
        description: Unauthorized - Invalid or missing API key.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Too many requests
                data: {}
              id: '1'
        description: Too Many Requests - Rate limit exceeded.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
                data: {}
              id: '1'
        description: Internal Server Error - An error occurred on the server.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
                data: {}
              id: '1'
        description: Service Unavailable - The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
                data: {}
              id: '1'
        description: Gateway Timeout - The request timed out.
  deprecated: false
  type: path
components:
  schemas: {}

````# getMinimumBalanceForRentExemption

> Returns minimum balance required to make account rent exempt.

## OpenAPI

````yaml openapi/rpc-http/getMinimumBalanceForRentExemption.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
                    default: '2.0'
              id:
                allOf:
                  - type: string
                    description: A unique identifier for the request.
                    example: '1'
                    default: '1'
              method:
                allOf:
                  - type: string
                    description: The name of the RPC method to invoke.
                    enum:
                      - getMinimumBalanceForRentExemption
                    example: getMinimumBalanceForRentExemption
                    default: getMinimumBalanceForRentExemption
              params:
                allOf:
                  - type: array
                    description: Parameters for the method.
                    default:
                      - 50
                    items:
                      oneOf:
                        - type: integer
                          description: >-
                            Size of account data in bytes that you need to store
                            on the Solana blockchain.
                          example: 50
                        - type: object
                          description: Configuration object with additional options.
                          properties:
                            commitment:
                              type: string
                              description: The commitment level for the request.
                              enum:
                                - confirmed
                                - finalized
                                - processed
                              example: finalized
            required: true
            requiredProperties:
              - jsonrpc
              - id
              - method
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getMinimumBalanceForRentExemption
              params:
                - 50
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              id:
                allOf:
                  - type: string
                    description: Identifier matching the request.
                    example: '1'
              result:
                allOf:
                  - type: integer
                    description: >-
                      Minimum SOL balance in lamports required to make an
                      account with this data size permanently rent-exempt.
                    example: 500
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              result: 500
        description: Successfully retrieved the minimum balance for rent exemption.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: The error code.
                        example: -32602
                      message:
                        type: string
                        description: The error message.
                      data:
                        type: object
                        description: Additional data about the error.
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Identifier matching the request.
                    example: '1'
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
              id: '1'
        description: Bad Request - Invalid request parameters or malformed request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
              id: '1'
        description: Unauthorized - Invalid or missing API key.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Too many requests
              id: '1'
        description: Too Many Requests - Rate limit exceeded.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
              id: '1'
        description: Internal Server Error - An error occurred on the server.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
              id: '1'
        description: Service Unavailable - The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
              id: '1'
        description: Gateway Timeout - The request timed out.
  deprecated: false
  type: path
components:
  schemas: {}

````# getMinimumBalanceForRentExemption

> Returns minimum balance required to make account rent exempt.

## OpenAPI

````yaml openapi/rpc-http/getMinimumBalanceForRentExemption.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
                    default: '2.0'
              id:
                allOf:
                  - type: string
                    description: A unique identifier for the request.
                    example: '1'
                    default: '1'
              method:
                allOf:
                  - type: string
                    description: The name of the RPC method to invoke.
                    enum:
                      - getMinimumBalanceForRentExemption
                    example: getMinimumBalanceForRentExemption
                    default: getMinimumBalanceForRentExemption
              params:
                allOf:
                  - type: array
                    description: Parameters for the method.
                    default:
                      - 50
                    items:
                      oneOf:
                        - type: integer
                          description: >-
                            Size of account data in bytes that you need to store
                            on the Solana blockchain.
                          example: 50
                        - type: object
                          description: Configuration object with additional options.
                          properties:
                            commitment:
                              type: string
                              description: The commitment level for the request.
                              enum:
                                - confirmed
                                - finalized
                                - processed
                              example: finalized
            required: true
            requiredProperties:
              - jsonrpc
              - id
              - method
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getMinimumBalanceForRentExemption
              params:
                - 50
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              id:
                allOf:
                  - type: string
                    description: Identifier matching the request.
                    example: '1'
              result:
                allOf:
                  - type: integer
                    description: >-
                      Minimum SOL balance in lamports required to make an
                      account with this data size permanently rent-exempt.
                    example: 500
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              result: 500
        description: Successfully retrieved the minimum balance for rent exemption.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: The error code.
                        example: -32602
                      message:
                        type: string
                        description: The error message.
                      data:
                        type: object
                        description: Additional data about the error.
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Identifier matching the request.
                    example: '1'
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
              id: '1'
        description: Bad Request - Invalid request parameters or malformed request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
              id: '1'
        description: Unauthorized - Invalid or missing API key.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Too many requests
              id: '1'
        description: Too Many Requests - Rate limit exceeded.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
              id: '1'
        description: Internal Server Error - An error occurred on the server.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
              id: '1'
        description: Service Unavailable - The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
              id: '1'
        description: Gateway Timeout - The request timed out.
  deprecated: false
  type: path
components:
  schemas: {}

````# getMinimumBalanceForRentExemption

> Returns minimum balance required to make account rent exempt.

## OpenAPI

````yaml openapi/rpc-http/getMinimumBalanceForRentExemption.yaml post /
paths:
  path: /
  method: post
  servers:
    - url: https://mainnet.helius-rpc.com
      description: Mainnet RPC endpoint
    - url: https://devnet.helius-rpc.com
      description: Devnet RPC endpoint
  request:
    security:
      - title: ApiKeyQuery
        parameters:
          query:
            api-key:
              type: apiKey
              description: >-
                Your Helius API key. You can get one for free in the
                [dashboard](https://dashboard.helius.dev/api-keys).
          header: {}
          cookie: {}
    parameters:
      path: {}
      query: {}
      header: {}
      cookie: {}
    body:
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
                    default: '2.0'
              id:
                allOf:
                  - type: string
                    description: A unique identifier for the request.
                    example: '1'
                    default: '1'
              method:
                allOf:
                  - type: string
                    description: The name of the RPC method to invoke.
                    enum:
                      - getMinimumBalanceForRentExemption
                    example: getMinimumBalanceForRentExemption
                    default: getMinimumBalanceForRentExemption
              params:
                allOf:
                  - type: array
                    description: Parameters for the method.
                    default:
                      - 50
                    items:
                      oneOf:
                        - type: integer
                          description: >-
                            Size of account data in bytes that you need to store
                            on the Solana blockchain.
                          example: 50
                        - type: object
                          description: Configuration object with additional options.
                          properties:
                            commitment:
                              type: string
                              description: The commitment level for the request.
                              enum:
                                - confirmed
                                - finalized
                                - processed
                              example: finalized
            required: true
            requiredProperties:
              - jsonrpc
              - id
              - method
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              method: getMinimumBalanceForRentExemption
              params:
                - 50
  response:
    '200':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              id:
                allOf:
                  - type: string
                    description: Identifier matching the request.
                    example: '1'
              result:
                allOf:
                  - type: integer
                    description: >-
                      Minimum SOL balance in lamports required to make an
                      account with this data size permanently rent-exempt.
                    example: 500
        examples:
          example:
            value:
              jsonrpc: '2.0'
              id: '1'
              result: 500
        description: Successfully retrieved the minimum balance for rent exemption.
    '400':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - &ref_0
                    type: string
                    description: The JSON-RPC protocol version.
                    enum:
                      - '2.0'
                    example: '2.0'
              error:
                allOf:
                  - &ref_1
                    type: object
                    properties:
                      code:
                        type: integer
                        description: The error code.
                        example: -32602
                      message:
                        type: string
                        description: The error message.
                      data:
                        type: object
                        description: Additional data about the error.
              id:
                allOf:
                  - &ref_2
                    type: string
                    description: Identifier matching the request.
                    example: '1'
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32602
                message: Invalid params
              id: '1'
        description: Bad Request - Invalid request parameters or malformed request.
    '401':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32001
                message: Unauthorized
              id: '1'
        description: Unauthorized - Invalid or missing API key.
    '429':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32005
                message: Too many requests
              id: '1'
        description: Too Many Requests - Rate limit exceeded.
    '500':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32603
                message: Internal error
              id: '1'
        description: Internal Server Error - An error occurred on the server.
    '503':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32002
                message: Service unavailable
              id: '1'
        description: Service Unavailable - The service is temporarily unavailable.
    '504':
      application/json:
        schemaArray:
          - type: object
            properties:
              jsonrpc:
                allOf:
                  - *ref_0
              error:
                allOf:
                  - *ref_1
              id:
                allOf:
                  - *ref_2
            refIdentifier: '#/components/schemas/ErrorResponse'
        examples:
          example:
            value:
              jsonrpc: '2.0'
              error:
                code: -32003
                message: Gateway timeout
              id: '1'
        description: Gateway Timeout - The request timed out.
  deprecated: false
  type: path
components:
  schemas: {}

````{
  "jsonrpc": "2.0",
  "error": {
    "code": -32005,
    "message": "Too many requests"
  },
  "id": "1"
}{
  "jsonrpc": "2.0",
  "error": {
    "code": -32603,
    "message": "Internal error"
  },
  "id": "1"
}{
  "jsonrpc": "2.0",
  "error": {
    "code": -32002,
    "message": "Service unavailable"
  },
  "id": "1"
}{
  "jsonrpc": "2.0",
  "error": {
    "code": -32003,
    "message": "Gateway timeout"
  },
  "id": "1"
}{
  "jsonrpc": "2.0",
  "id": "1",
  "result": {
    "context": {
      "slot": 54
    },
    "value": [
      {
        "lamports": 999974,
        "address": "99P8ZgtJYe1buSK8JXkvpLh8xPsCFuLYhz9hQFNw93WJ"
      }
    ]
  }
}{
  "jsonrpc": "2.0",
  "error": {
    "code": -32602,
    "message": "Invalid params"
  },
  "id": "1"
}{
  "jsonrpc": "2.0",
  "error": {
    "code": -32001,
    "message": "Unauthorized"
  },
  "id": "1"
}
========================
CODE SNIPPETS
========================
TITLE: Install pnpm and project dependencies for Helius SDK
DESCRIPTION: Instructions to install pnpm globally if not already present, followed by installing all project dependencies for the Helius Node.js SDK using pnpm.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/CONTRIBUTING.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install -g pnpm

pnpm install
```

----------------------------------------

TITLE: Get Stake Account Creation and Delegation Instructions
DESCRIPTION: Retrieves the necessary instructions for creating and delegating a new Solana stake account. The method returns both the instructions required to perform the operation and the public key of the newly generated stake account.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_11

LANGUAGE: ts
CODE:
```
const { instructions, stakeAccount } = await helius.rpc.getStakeInstructions(
  ownerPubkey,
  1.5 // Amount in SOL (excluding rent exemption)
);
```

----------------------------------------

TITLE: Get digital assets by group key and value
DESCRIPTION: Retrieves a list of assets belonging to a specific group, identified by a group key (e.g., 'collection') and its corresponding value. This endpoint is very useful for getting the mint list for NFT Collections or other grouped assets.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_7

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.getAssetsByGroup({
  groupKey: 'collection',
  groupValue: 'J1S9H3QjnRtBbbuD4HjPV6RpRhwuk4zKbxsnCHuTgh9w',
  page: 1,
});
console.log(response.items);
```

----------------------------------------

TITLE: Mint Compressed NFT (cNFT) with Helius SDK
DESCRIPTION: Demonstrates how to mint a compressed NFT on Solana using the Helius SDK's `mintCompressedNft` method. The example includes detailed metadata such as name, symbol, owner, description, attributes, and image URL for the NFT.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_16

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.mintCompressedNft({
  name: 'Exodia the Forbidden One',
  symbol: 'ETFO',
  owner: 'OWNER_WALLET_ADDRESS',
  description:
    'Exodia the Forbidden One is a powerful, legendary creature composed of five parts: the Right Leg, Left Leg, Right Arm, Left Arm, and the Head. When all five parts are assembled, Exodia becomes an unstoppable force.',
  attributes: [
    {
      trait_type: 'Type',
      value: 'Legendary'
    },
    {
      trait_type: 'Power',
      value: 'Infinite'
    },
    {
      trait_type: 'Element',
      value: 'Dark'
    },
    {
      trait_type: 'Rarity',
      value: 'Mythical'
    }
  ],
  imageUrl:
    'https://cdna.artstation.com/p/assets/images/images/052/118/830/large/julie-almoneda-03.jpg?1658992401',
  externalUrl: 'https://www.yugioh-card.com/en/',
  sellerFeeBasisPoints: 6900
});
console.log(response.result);
```

----------------------------------------

TITLE: Get Jito Bundle Statuses with Helius SDK
DESCRIPTION: This method gets the status of Jito bundles. It takes in an array of bundle IDs and a Jito Block Engine API URL. It returns the status of the bundles.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_36

LANGUAGE: typescript
CODE:
```
const bundleIds = [
  /* Bundle IDs */
];
const jitoApiUrl = 'https://mainnet.block-engine.jito.wtf/api/v1/bundles';
const statuses = helius.rpc.getBundleStatuses(bundleIds, jitoApiUrl);
```

----------------------------------------

TITLE: Install Helius SDK with npm or yarn
DESCRIPTION: Instructions for installing the Helius SDK package using Node.js package managers. Choose between npm or yarn commands to add the SDK to your project dependencies.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
npm install helius-sdk
```

LANGUAGE: Shell
CODE:
```
yarn add helius-sdk
```

----------------------------------------

TITLE: Build and test Helius SDK project
DESCRIPTION: Commands to compile the Helius Node.js SDK project and run its associated unit tests, ensuring all functionality works as expected before a pull request.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/CONTRIBUTING.md#_snippet_1

LANGUAGE: bash
CODE:
```
pnpm build
pnpm test
```

----------------------------------------

TITLE: Get all digital assets owned by an address
DESCRIPTION: Retrieves a list of all digital assets, including NFTs, owned by a given Solana wallet address. This method is described as the fastest way to get all NFTs for a wallet, making it ideal for displaying user portfolios.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_6

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.getAssetsByOwner({
  ownerAddress: '86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY',
  page: 1,
});
console.log(response.items);
```

----------------------------------------

TITLE: Initialize Helius SDK and fetch assets by owner
DESCRIPTION: Demonstrates how to configure the Helius SDK with your API key and perform an asynchronous call to getAssetsByOwner. This example retrieves assets associated with a specified wallet address.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import { Helius } from 'helius-sdk';

// Replace YOUR_API_KEY with the API key from your Helius dashboard
const helius = new Helius('YOUR_API_KEY');

const getAssetsByOwner = async () => {
  const response = await helius.rpc.getAssetsByOwner({
    ownerAddress: '86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY',
    page: 1,
  });
  console.log(response.items);
};

getAssetsByOwner();
```

----------------------------------------

TITLE: Send 0.5 SOL with Helius sendSmartTransaction (TypeScript)
DESCRIPTION: This TypeScript example demonstrates how to use `sendSmartTransaction` to send 0.5 SOL to a public key. It initializes the Helius SDK, creates a system transfer instruction, and then sends the transaction with `skipPreflight` enabled.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_31

LANGUAGE: TypeScript
CODE:
```
import { Helius } from "helius-sdk";
import {
  Keypair,
  SystemProgram,
  LAMPORTS_PER_SOL,
  TransactionInstruction,
} from "@solana/web3.js";

const helius = new Helius("YOUR_API_KEY");

const fromKeypair = /* Your keypair goes here */;
const fromPubkey = fromKeypair.publicKey;
const toPubkey = /* The person we're sending 0.5 SOL to */;

const instructions: TransactionInstruction[] = [
  SystemProgram.transfer({
    fromPubkey: fromPubkey,
    toPubkey: toPubkey,
    lamports: 0.5 * LAMPORTS_PER_SOL,
  }),
];

const transactionSignature = await helius.rpc.sendSmartTransaction(instructions, [fromKeypair], [], { skipPreflight: true });
console.log(`Successful transfer: ${transactionSignature}`);
```

----------------------------------------

TITLE: Get a Merkle proof for a compressed asset
DESCRIPTION: Retrieves the Merkle proof for a compressed asset using its ID. This proof is essential for verifying the integrity and existence of compressed NFTs on-chain, allowing for client-side validation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_5

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.getAssetProof({
  id: 'Bu1DEKeawy7txbnCEJE4BU3BKLXaNAKCYcHR4XhndGss',
});
console.log(response);
```

----------------------------------------

TITLE: Execute Jupiter Token Swap with Helius SDK
DESCRIPTION: This TypeScript code demonstrates how to perform a token swap using Jupiter Exchange via the Helius SDK. It includes automatic transaction optimizations like priority fees, compute unit calculation, slippage tolerance, and reliable transaction confirmation. The example swaps SOL to USDC and logs the result upon success.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_46

LANGUAGE: typescript
CODE:
```
import { Helius } from 'helius-sdk';
import { Keypair } from '@solana/web3.js';

const helius = new Helius('YOUR_API_KEY');

// Swap SOL to USDC with transaction landing optimizations
const result = await helius.rpc.executeJupiterSwap({
  inputMint: 'So********************************111111112', // SOL
  outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
  amount: 10000000, // 0.01 SOL (SOL has 9 decimals)
  slippageBps: 50, // 0.5% slippage tolerance
  restrictIntermediateTokens: true, // Improves pricing
  priorityLevel: 'high', // Options: 'medium' (25th percentile), 'high' (50th percentile), 'veryHigh' (75th percentile)
  maxPriorityFeeLamports: 1000000, // Caps priority fee at 0.001 SOL
  skipPreflight: true, // Skip preflight checks
  confirmationCommitment: 'confirmed' // Wait for confirmation
}, wallet);

if (result.success && result.confirmed) {
  console.log(`Received ${result.outputAmount} USDC, tx: ${result.signature}`);
}
```

----------------------------------------

TITLE: Get All Tokens for an NFT Collection
DESCRIPTION: Retrieves a list of all tokens (mints) associated with a specific NFT collection using the Helius SDK. This method allows querying by predefined collection identifiers to fetch all NFTs within that collection.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_18

LANGUAGE: ts
CODE:
```
import {
  Helius,
  Collections
} from "helius-sdk";

const helius = new Helius("YOUR_API_KEY");

const mints = helius.getMintlist({
  query: Collections.ABC
});
```

----------------------------------------

TITLE: Get digital assets created by an address
DESCRIPTION: Retrieves a list of assets created by a specific creator address. This method allows filtering for only verified assets, providing flexibility for different use cases.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_8

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.getAssetsByCreator({
  creatorAddress: 'D3XrkNZz6wx6cofot7Zohsf2KSsu2ArngNk8VqU9cTY3',
  onlyVerified: true,
  page: 1,
});
console.log(response.items);
```

----------------------------------------

TITLE: Get digital assets with a specific authority
DESCRIPTION: Retrieves a list of assets that have a particular authority address. This is useful for managing assets where specific permissions or control are delegated to an authority.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_9

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.getAssetsByAuthority({
  authorityAddress: '2RtGg6fsFiiF1EQzHqbd66AhW7R5bWeQGpTbv2UMkCdW',
  page: 1,
});
console.
```

----------------------------------------

TITLE: Install Node.js Type Definitions with npm
DESCRIPTION: Use npm to install the `@types/node` package, which provides TypeScript type definitions for Node.js, saving it as a project dependency.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/tests/node_modules/@types/node/README.md#_snippet_0

LANGUAGE: npm
CODE:
```
npm install --save @types/node
```

----------------------------------------

TITLE: Helius SDK addTipInstruction Method (APIDOC)
DESCRIPTION: Documents the `addTipInstruction` method, which appends a tip transfer instruction to an existing array of transaction instructions. It requires the instruction array, fee payer, tip account, and tip amount.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_32

LANGUAGE: APIDOC
CODE:
```
Method: addTipInstruction
  Description: Adds a tip instruction to the last instruction in the set of provided instructions. It is a transfer instruction that sends the specified amount of lamports from the fee payer to the designated tip account.
  Parameters:
    instructions: TransactionInstruction[] - The array of instructions to which the tip instruction will be added.
    feePayer: Signer - The signer that will pay the tip.
    tipAccount: PublicKey - The public key of the designated tip account.
    tipAmount: number - The amount of lamports to send as a tip.
  Returns: void
```

----------------------------------------

TITLE: Search for digital assets by various parameters
DESCRIPTION: Allows searching for digital assets based on criteria such as owner address, compression status, and pagination. This method is particularly useful for implementing features like token-gating or displaying user-specific asset collections.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_4

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.searchAssets({
  ownerAddress: '2k5AXX4guW9XwRQ1AKCpAuUqgWDpQpwFfpVFh3hnm2Ha',
  compressed: true,
  page: 1,
});
console.log(response.items);
```

----------------------------------------

TITLE: Get Token Holders by Mint Address with Helius SDK
DESCRIPTION: This method retrieves the token holders for a specified token mint address using the Helius SDK.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_42

LANGUAGE: typescript
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

const response = await helius.rpc.getTokenHolders('<token mint address>');
```

----------------------------------------

TITLE: Format and lint Helius SDK code
DESCRIPTION: Commands to automatically format and lint the Helius Node.js SDK codebase, ensuring adherence to established style guidelines and identifying potential code quality issues.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/CONTRIBUTING.md#_snippet_2

LANGUAGE: bash
CODE:
```
pnpm format
pnpm lint
```

----------------------------------------

TITLE: Helius SDK createSmartTransactionWithTip Method (APIDOC)
DESCRIPTION: Documents the `createSmartTransactionWithTip` method, which generates a serialized smart transaction including a Jito tip. It takes instructions, signers, optional lookup tables, a tip amount, and an optional fee payer, returning the serialized transaction and its `lastValidBlockHeight`.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_34

LANGUAGE: APIDOC
CODE:
```
Method: createSmartTransactionWithTip
  Description: Creates a serialized smart transaction with a tip. This method abstracts the transaction creation functionality.
  Parameters:
    instructions: TransactionInstruction[] - An array of instructions for the transaction.
    signers: Signer[] - An array of signers for the transaction.
    lookupTables: AddressLookupTableAccount[] (Optional) - An array of lookup tables for versioned transactions.
    tipAmount: number (Optional, default=1000) - The amount of lamports for the tip. Defaults to 1000 lamports.
    feePayer: Signer (Optional) - The fee payer for the transaction.
  Returns: Promise<{ serializedTransaction: string; lastValidBlockHeight: number }>
    serializedTransaction: string - The serialized transaction as a string.
    lastValidBlockHeight: number - The last valid block height for the transaction.
```

----------------------------------------

TITLE: Get Stake Accounts by Wallet Address with Helius SDK
DESCRIPTION: This method retrieves stake accounts associated with a given wallet address using the Helius SDK.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_41

LANGUAGE: typescript
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

const response = await helius.rpc.getStakeAccounts('<wallet address>');
```

----------------------------------------

TITLE: Get Current Solana TPS with Helius SDK
DESCRIPTION: This method retrieves the current Transactions Per Second (TPS) on the Solana network using the Helius SDK.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_39

LANGUAGE: typescript
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

const tps = await helius.rpc.getCurrentTPS();
```

----------------------------------------

TITLE: Get Compute Units for Solana Transaction (TypeScript)
DESCRIPTION: This snippet shows how to simulate a transaction to determine the total compute units consumed. It takes transaction instructions, a fee payer, lookup tables, and signers, returning the compute units or null if the simulation is unsuccessful.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_27

LANGUAGE: TypeScript
CODE:
```
const units = helius.rpc.getComputeUnits(instructions, payerKey, [], []);
```

----------------------------------------

TITLE: Perform SOL Airdrop using Helius SDK
DESCRIPTION: This method performs an airdrop of SOL to a specified wallet address using the Helius SDK. It requires a public key and the amount in lamports.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_40

LANGUAGE: typescript
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

const response = await helius.rpc.airdrop(
  new PublicKey('<wallet address>'),
  *********0
); // 1 sol
```

----------------------------------------

TITLE: Estimate Solana Priority Fees with Helius SDK
DESCRIPTION: This method estimates priority fees, considering both global and local fee markets. Users can specify to receive all priority levels and adjust the lookback window for calculation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_43

LANGUAGE: typescript
CODE:
```
import { Helius } from 'helius-sdk';
const helius = new Helius('YOUR_API_KEY');

const response = await helius.rpc.getPriorityFeeEstimate({
  accountKeys: ['JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4'],
  options: {
    includeAllPriorityFeeLevels: true,
  },
});

console.log(response);
```

----------------------------------------

TITLE: Create Smart Transaction with Jito Tip (TypeScript)
DESCRIPTION: This method creates a serialized smart transaction that includes a Jito tip. It takes instructions, signers, optional lookup tables, a tip amount (defaulting to 1000 lamports), and an optional fee payer. It returns the serialized transaction string and the `lastValidBlockHeight`.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_35

LANGUAGE: TypeScript
CODE:
```
const { serializedTransaction, lastValidBlockHeight } =
  await this.createSmartTransactionWithTip(
    instructions,
    signers,
    lookupTables,
    tipAmount,
    feePayer
  );
```

----------------------------------------

TITLE: Helius SDK sendSmartTransaction Method (APIDOC)
DESCRIPTION: Documents the `sendSmartTransaction` method, which builds, sends, and handles the confirmation of optimized transactions. It allows extensive configuration for instructions, signers, lookup tables, and various sending options.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_30

LANGUAGE: APIDOC
CODE:
```
Method: sendSmartTransaction
  Description: Builds and sends an optimized transaction, handling its confirmation status.
  Parameters:
    instructions: TransactionInstruction[] - Array of instructions to be executed in the transaction.
    signers: Signer[] - Array of signers for the transaction. The first signer is used as the fee payer unless specified otherwise.
    lookupTables: AddressLookupTableAccount[] (Optional, default=[]) - Array of lookup tables for versioned transactions.
    options: SendSmartTransactionOptions (Optional) - Configuration options:
      lastValidBlockHeightOffset (number, optional, default=150): Offset added to current block height to compute expiration. Must be positive.
      pollTimeoutMs (number, optional, default=60000): Total timeout (ms) for confirmation polling.
      pollIntervalMs (number, optional, default=2000): Interval (ms) between polling attempts.
      pollChunkMs (number, optional, default=10000): Timeout (ms) for each individual polling chunk.
      skipPreflight (boolean, optional, default=false): Skip preflight transaction checks if true.
      preflightCommitment (Commitment, optional, default='confirmed'): Commitment level for preflight checks.
      maxRetries (number, optional): Maximum number of retries for sending the transaction.
      minContextSlot (number, optional): Minimum slot at which to fetch blockhash (prevents stale blockhash usage).
      feePayer (Signer, optional): Override fee payer (defaults to first signer).
      priorityFeeCap (number, optional): Maximum priority fee to pay in microlamports (for fee estimation capping).
      serializeOptions (SerializeConfig, optional): Custom serialization options for the transaction.
  Returns: Promise<string> - The transaction signature upon successful sending and confirmation.
```

----------------------------------------

TITLE: Manage Solana Connection with Helius RPC
DESCRIPTION: This TypeScript example shows how to use the `helius.connection` object to interact with the Solana blockchain via your Helius RPC. It leverages methods from Solana-Web3.js (version 1.73.2) for standard blockchain operations, such as fetching the current block height. This provides a direct interface for common Solana API calls.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_47

LANGUAGE: typescript
CODE:
```
import { Helius } from 'helius-sdk';

// Replace YOUR_API_KEY with the API key from your Helius dashboard
const helius = new Helius('YOUR_API_KEY');

const getBlockHeight = async () => {
  const response = await helius.connection.getBlockHeight();
  console.log(response);
};

getBlockHeight();
```

----------------------------------------

TITLE: Get transaction signatures for a compressed asset
DESCRIPTION: Retrieves a list of transaction signatures associated with a specific compressed asset. This is useful for tracking the history and activity of a compressed NFT or digital asset on the blockchain. Supports pagination for large result sets.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_3

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.getSignaturesForAsset({
  id: 'Bu1DEKeawy7txbnCEJE4BU3BKLXaNAKCYcHR4XhndGss',
  page: 1,
});
console.log(response.items);
```

----------------------------------------

TITLE: Retrieve All Helius Webhooks (TypeScript)
DESCRIPTION: This snippet shows how to retrieve a list of all webhooks associated with your Helius API key. This is useful for auditing or managing all active webhooks.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_25

LANGUAGE: TypeScript
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.getAllWebhooks();
```

----------------------------------------

TITLE: Get Withdraw Instruction for Stake Account
DESCRIPTION: Generates a single instruction to withdraw a specified amount of lamports from a Solana stake account to a destination address. This requires the owner's public key, the stake account's public key, the destination public key, and the amount to withdraw.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_14

LANGUAGE: ts
CODE:
```
const ix = helius.rpc.getWithdrawInstruction(
  ownerPubkey,
  stakeAccountPubkey,
  destinationPubkey,
  lamportsToWithdraw
);
```

----------------------------------------

TITLE: Get Withdrawable Lamports from Stake Account
DESCRIPTION: Calculates and returns the amount of lamports that can be withdrawn from a specified stake account. An optional boolean parameter allows including the rent-exempt amount in the total calculation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_13

LANGUAGE: ts
CODE:
```
const withdrawable = await helius.rpc.getWithdrawableAmount(stakeAccountPubkey, true);
```

----------------------------------------

TITLE: Retrieve multiple digital assets by ID in a batch
DESCRIPTION: Fetches up to 1,000 digital assets simultaneously using a list of their IDs. This method is highly efficient for retrieving information on multiple assets at once, reducing the number of API calls.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_2

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const ids = [
  'F9Lw3ki3hJ7PF9HQXsBzoY8GyE6sPoEZZdXJBsTTD2rk',
  'F9Lw3ki3hJ7PF9HQXsBzoY8GyE6sPoEZZdXJBsTTD2rk',
];
const helius = new Helius('your-api-key');
const response = await helius.rpc.getAssetBatch({
  ids: ids,
});
console.log(response.map((x) => x.id));
```

----------------------------------------

TITLE: Delegate and Revoke Collection Authority for cNFT Minting
DESCRIPTION: Illustrates the process of delegating Helius as a collection authority to mint cNFTs into a specific collection, followed by an example of minting a cNFT to that collection, and finally, revoking the delegated authority. This is crucial for managing cNFTs within a collection.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_17

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

// 1. Delegate Helius as a collection authority
await helius.delegateCollectionAuthority({
  collectionMint: 'COLLECTION_MINT_ADDRESS',
  updateAuthorityKeypair: updateAuthorityKeypair
});

// 2. Mint your cNFT to the collection
const response = await helius.mintCompressedNft({
  name: 'Feitan Portor',
  symbol: 'FEITAN',
  owner: 'OWNER_WALLET_ADDRESS',
  collection: 'COLLECTION_MINT_ADDRESS',
  description: 'Feitan Portor is a member of the notorious Phantom Troupe.',
  attributes: [
    {
      trait_type: 'Affiliation',
      value: 'Phantom Troupe'
    },
    {
      trait_type: 'Nen Ability',
      value: 'Pain Packer'
    }
  ],
  externalUrl: 'https://hunterxhunter.fandom.com/wiki/Feitan_Portor',
  imagePath: '../images/feitan.png',
  walletPrivatekey: 'YOUR_WALLET_PRIVATE_KEY'
});

// 3. Revoke collection authority (optional)
await helius.revokeCollectionAuthority({
  collectionMint: 'COLLECTION_MINT_ADDRESS',
  revokeAuthorityKeypair: revokeAuthorityKeypair
});
```

----------------------------------------

TITLE: Get a single digital asset by ID using Helius SDK
DESCRIPTION: Retrieves a single digital asset using its unique ID. This method allows specifying display options, such as showing collection metadata, to tailor the returned asset information.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_1

LANGUAGE: ts
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');
const response = await helius.rpc.getAsset({
  id: 'F9Lw3ki3hJ7PF9HQXsBzoY8GyE6sPoEZZdXJBsTTD2rk',
  displayOptions: {
    showCollectionMetadata: true,
  },
});
console.log(response.grouping?.map((g) => g.collection_metadata?.name));
```

----------------------------------------

TITLE: General Solana Helper Methods
DESCRIPTION: Offers utility functions for various Solana-related tasks, including retrieving current TPS, requesting airdrops, and getting stake accounts.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_11

LANGUAGE: APIDOC
CODE:
```
getCurrentTPS(): Returns the current transactions per second (TPS) rate — including voting transactions.
```

LANGUAGE: APIDOC
CODE:
```
airdrop(): Request an allocation of lamports to the specified address.
```

LANGUAGE: APIDOC
CODE:
```
getStakeAccounts(): Returns all the stake accounts for a given public key.
```

----------------------------------------

TITLE: Get Unstake Instruction for Solana Account
DESCRIPTION: Provides a single instruction to deactivate a Solana stake account, preparing it for withdrawal. This function requires the public key of the stake account owner and the public key of the stake account itself.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_12

LANGUAGE: ts
CODE:
```
const ix = helius.rpc.getUnstakeInstruction(ownerPubkey, stakeAccountPubkey);
```

----------------------------------------

TITLE: Broadcast and Confirm Solana Transaction with Helius SDK
DESCRIPTION: This method broadcasts a fully signed transaction and polls for its confirmation, whether it's an object or serialized. It automatically extracts the `recentBlockhash` if a `Transaction` object is passed. The example demonstrates building, serializing, and broadcasting a versioned transaction.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_45

LANGUAGE: typescript
CODE:
```
async function main() {
    // Build a versioned transaction
    const { blockhash } = await helius.connection.getLatestBlockhash();

    const messageV0 = new TransactionMessage({
      payerKey: payer.publicKey,
      recentBlockhash: blockhash,
      instructions: [
        SystemProgram.transfer({
          fromPubkey: payer.publicKey,
          toPubkey: recipient,
          lamports: 1000,
        }),
      ],
    }).compileToV0Message();

    const vtx = new VersionedTransaction(messageV0);
  vtx.sign([payer]);

  // Serialize to Buffer
  const serializedBuffer = Buffer.from(vtx.serialize());

  // Broadcast
  const signature = await helius.rpc.broadcastTransaction(serializedBuffer);
  console.log('Broadcast serialized (Buffer) signature:', signature);
  }
```

----------------------------------------

TITLE: Retrieve Helius Webhook by ID (TypeScript)
DESCRIPTION: This snippet demonstrates how to fetch the details of a specific Helius webhook using its unique identifier. It allows for retrieving the configuration and status of an individual webhook.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_24

LANGUAGE: TypeScript
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.getWebhookByID('<webhook-id-here>');
```

----------------------------------------

TITLE: Helius SDK RPC Namespace Overview
DESCRIPTION: The `helius.rpc` namespace within the Helius SDK provides a collection of methods for interacting with the Solana blockchain, particularly for Digital Asset Standard (DAS) functionalities. These methods allow for querying, searching, and managing digital assets and related data.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Namespace: `helius.rpc`

This namespace contains methods for:
- Retrieving single or batch assets
- Searching assets by various criteria
- Getting asset proofs and transaction signatures
- Managing assets by owner, group, creator, or authority
- Querying token accounts and NFT editions
```

----------------------------------------

TITLE: Add Jito Tip Instruction to Transaction (TypeScript)
DESCRIPTION: This method adds a tip instruction to the provided array of transaction instructions. It creates a transfer instruction from the fee payer to a randomly selected Jito tip account with a specified tip amount.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_33

LANGUAGE: TypeScript
CODE:
```
const randomTipAccount =
  JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)];
const tipAmount = 10000;

helius.rpc.addTipInstruction(
  instructions,
  feePayer,
  randomTipAccount,
  tipAmount
);
```

----------------------------------------

TITLE: Get Priority Fee Estimate API Reference
DESCRIPTION: Returns an estimated priority fee based on a set of predefined priority levels (percentiles).

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_13

LANGUAGE: APIDOC
CODE:
```
getPriorityFeeEstimate():
  description: Returns an estimated priority fee based on a set of predefined priority levels (percentiles).
```

----------------------------------------

TITLE: Fetch Helius Delegated Stake Accounts
DESCRIPTION: Retrieves all Solana stake accounts associated with a given wallet address that are currently delegated to the Helius validator. This helps in identifying and managing stake accounts under Helius's delegation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_15

LANGUAGE: ts
CODE:
```
const heliusStakeAccounts = await helius.rpc.getHeliusStakeAccounts(walletAddress);
```

----------------------------------------

TITLE: Get Token Holders API Reference
DESCRIPTION: Returns all the token accounts for a given mint address (ONLY FOR SPL TOKENS).

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_12

LANGUAGE: APIDOC
CODE:
```
getTokenHolders():
  description: Returns all the token accounts for a given mint address (ONLY FOR SPL TOKENS).
```

----------------------------------------

TITLE: Append Addresses to Helius Webhook (TypeScript)
DESCRIPTION: This snippet provides a convenient way to add new account addresses to an existing Helius webhook without overwriting other configurations. It simplifies the process of expanding the watch list for a webhook.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_21

LANGUAGE: TypeScript
CODE:
```
import { Helius, Address } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.appendAddressesToWebhook('your-webhook-id-here', [
  Address.SQUADS,
  Address.JUPITER_V3,
]);
```

----------------------------------------

TITLE: Create Helius Smart Transaction (TypeScript)
DESCRIPTION: This snippet demonstrates how to create a smart transaction using the Helius SDK's RPC functionality. It takes an array of instructions, signers, lookup tables, and an optional fee payer, returning the transaction object and its last valid block height.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_26

LANGUAGE: TypeScript
CODE:
```
const { smartTransaction: transaction, lastValidBlockHeight } =
  await helius.rpc.createSmartTransaction(
    instructions,
    signers,
    lookupTables,
    feePayer
  );
```

----------------------------------------

TITLE: Helius SDK pollTransactionConfirmation Method (APIDOC)
DESCRIPTION: Documents the `pollTransactionConfirmation` method, which checks if a transaction has been confirmed. It takes a `TransactionSignature` and allows configuration of timeout and retry intervals via `PollTransactionOptions`.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_28

LANGUAGE: APIDOC
CODE:
```
Method: pollTransactionConfirmation
  Description: Polls a transaction to check whether it has been confirmed.
  Parameters:
    txSig: TransactionSignature - The signature of the transaction to poll.
    options: PollTransactionOptions (optional) - Options to configure polling, including timeout and retry interval.
  Returns: Promise<TransactionSignature> - The confirmed transaction signature.
  Errors: Returns an error if confirmation times out.
```

----------------------------------------

TITLE: Send Jito Transaction Bundle using Helius SDK
DESCRIPTION: This method sends a bundle of transactions to the Jito Block Engine. It takes in an array of serialized transactions and a Jito Block Engine API URL. It returns the bundle ID as a string.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_37

LANGUAGE: typescript
CODE:
```
const jitoApiUrl = 'https://mainnet.block-engine.jito.wtf/api/v1/bundles';
const bundleId = helius.rpc.sendJitoBundle(
  [serializedTransactions],
  jitoApiUrl
);
```

----------------------------------------

TITLE: Create Helius Webhook for Solana Events (TypeScript)
DESCRIPTION: This snippet demonstrates how to create a Helius webhook to monitor specific Solana transaction types and account addresses. It shows both the default enhanced webhook and a raw webhook for native Solana data, including an optional authentication header.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_19

LANGUAGE: TypeScript
CODE:
```
import {
  // enums
  Address,
  TransactionType,

  // lib
  Helius,
} from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.createWebhook({
  accountAddresses: [Address.MAGIC_EDEN_V2],
  transactionTypes: [TransactionType.NFT_LISTING],
  webhookURL: 'my-webhook-handler.com/handle',
});
```

LANGUAGE: TypeScript
CODE:
```
import {
  // enums
  TransactionType,
  WebhookType,
  Address,
  Helius,
} from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.createWebhook({
  accountAddresses: [Address.MAGIC_EDEN_V2],
  authHeader: 'some auth header',
  webhookURL: 'my-webhook-handler.com/handle',
  webhookType: WebhookType.RAW,
  transactionTypes: [TransactionType.ANY],
});
```

----------------------------------------

TITLE: Helius Method: Get All Webhooks
DESCRIPTION: Documents the method to retrieve all webhooks associated with the current API key. It returns a promise that resolves to an array of webhook objects or throws an error if the API call fails.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/Helius.html#_snippet_5

LANGUAGE: APIDOC
CODE:
```
Method: getAllWebhooks()
  Description: Retrieves a list of all webhooks associated with the current API key
  Throws:
    Type: Error
    Description: if there is an error calling the webhooks endpoint or if the response contains an error
  Returns:
    Type: Promise.<Array.<Webhook>>
    Description: a promise that resolves to an array of webhook objects
```

----------------------------------------

TITLE: Send Smart Transaction with Jito Tip using Helius SDK
DESCRIPTION: This method has the same functionality as `sendSmartTransaction`. However, it sends the optimized transaction as a bundle and includes a tip so it is processed by Jito's Block Engine. The following code snippet sends 0.05 SOL to a given public key with a Jito tip of 100k lamports using Jito's New York API URL.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_38

LANGUAGE: typescript
CODE:
```
import { Helius } from "helius-sdk";
import {
  Keypair,
  SystemProgram,
  LAMPORTS_PER_SOL,
  TransactionInstruction,
} from "@solana/web3.js";

const helius = new Helius("YOUR_API_KEY");

const fromKeypair = /* Your keypair goes here */;
const fromPubkey = fromKeypair.publicKey;
const toPubkey = /* The person we're sending 0.05 SOL to */;

const instructions: TransactionInstruction[] = [
  SystemProgram.transfer({
    fromPubkey: fromPubkey,
    toPubkey: toPubkey,
    lamports: 0.05 * LAMPORTS_PER_SOL,
  }),
];

// Call the sendSmartTransactionWithTip function
const bundleId = await helius.rpc.sendSmartTransactionWithTip(instructions, [keypair], address_lut, 100000, "NY");
console.log(`Bundle sent successfully with ID: ${bundleId}`);
```

----------------------------------------

TITLE: Delete Helius Webhook by ID (TypeScript)
DESCRIPTION: This snippet shows how to permanently delete a Helius webhook using its unique identifier. The method returns a boolean indicating the success of the deletion operation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_23

LANGUAGE: TypeScript
CODE:
```
import { Helius } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.deleteWebhook('<webhook-id-here>'); // returns a boolean
```

----------------------------------------

TITLE: Helius Method: Get Webhook by ID
DESCRIPTION: Documents the method to retrieve a single webhook by its ID. It returns a promise that resolves to a webhook object or throws an error if the API call fails or the webhook is not found.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/Helius.html#_snippet_6

LANGUAGE: APIDOC
CODE:
```
Method: getWebhookByID(webhookID)
  Description: Retrieves a single webhook by its ID, associated with the current API key
  Parameters:
    webhookID:
      Type: string
      Description: the ID of the webhook to retrieve
  Throws:
    Type: Error
    Description: if there is an error calling the webhooks endpoint or if the response contains an error
  Returns:
    Type: Promise.<Webhook>
    Description: a promise that resolves to a webhook object
```

----------------------------------------

TITLE: Remove Addresses from Helius Webhook (TypeScript)
DESCRIPTION: This snippet demonstrates how to remove specific account addresses from an existing Helius webhook. It offers a convenient method for refining the watch list of a webhook by excluding certain addresses.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_22

LANGUAGE: TypeScript
CODE:
```
import { Helius, Address } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.removeAddressesFromWebhook('your-webhook-id-here', [
  Address.SQUADS,
  Address.JUPITER_V3,
]);
```

----------------------------------------

TITLE: Edit Existing Helius Webhook (TypeScript)
DESCRIPTION: This snippet shows how to modify an existing Helius webhook. It specifically demonstrates updating the `accountAddresses` field, but note that this method overwrites the specified fields, requiring careful merging with existing webhook data.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_20

LANGUAGE: TypeScript
CODE:
```
import { Helius, Address } from 'helius-sdk';

const helius = new Helius('YOUR_API_KEY');

helius.editWebhook(
  'your-webhook-id-here',
  { accountAddresses: [Address.SQUADS] } // This will ONLY update accountAddresses, not the other fields on the webhook object
);
```

----------------------------------------

TITLE: Send Solana Transaction with Validator ACLs using Helius SDK
DESCRIPTION: This method sends a Solana transaction, similar to the standard `sendTransaction`, but with added support for `validatorAcls` JSON-based allow/denylists. It includes error handling for the transaction sending process.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_44

LANGUAGE: typescript
CODE:
```
try {
  const response = await helius.rpc.sendTransaction(transaction, {
    validatorAcls: [SFDP_REJECTS_URL],
    skipPreflight: true,
  });
} catch (error) {
  console.error(error);
}
```

----------------------------------------

TITLE: Create Withdraw Transaction with Helius SDK
DESCRIPTION: Generates an unsigned transaction to withdraw funds from a Solana stake account after its cooldown period. This method requires the owner's public key, the stake account's public key, the destination public key, and the amount of lamports to withdraw.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_10

LANGUAGE: ts
CODE:
```
const serializedTx = await helius.rpc.createWithdrawTransaction(
  ownerPubkey,
  stakeAccountPubkey,
  destinationPubkey,
  lamportsToWithdraw
);
```

----------------------------------------

TITLE: Retrieve All Helius Webhooks (JavaScript)
DESCRIPTION: Fetches a list of all webhooks associated with the initialized Helius API key. This method makes a GET request to the Helius API's `/v0/webhooks` endpoint and includes robust error handling for network or API-specific issues, throwing a descriptive error message if the API call fails.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_2

LANGUAGE: javascript
CODE:
```
getAllWebhooks() {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { data } = yield axios_1.default.get(`${API_URL_V0}/webhooks?api-key=${this.apiKey}`);
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error calling getWebhooks: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error calling getWebhooks: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Handle Helius API errors using try-catch
DESCRIPTION: Illustrates how to implement robust error handling for Helius API requests. This TypeScript example uses a try-catch block to gracefully manage potential non-success responses (4xx or 5xx) from the API.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_2

LANGUAGE: TypeScript
CODE:
```
try {
  const response = await helius.rpc.getAssetsByOwner({
    ownerAddress: '86xCnPeV69n6t3DnyGvkKobf9FdN2H9oiVDdaMpo2MMY',
    page: 1,
  });
  console.log(response.items);
} catch (error) {
  console.log(error);
}
```

----------------------------------------

TITLE: Poll Transaction Confirmation with Helius SDK (TypeScript)
DESCRIPTION: This method polls a transaction signature to check its confirmation status within a specified timeout. It defaults to a 15-second timeout and 5-second retry interval, but these can be configured. It returns the confirmed signature or an error on timeout.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/examples/EXAMPLES_OVERVIEW.md#_snippet_29

LANGUAGE: TypeScript
CODE:
```
let txSig = await helius
  .connection()
  .sendRawTransaction(transaction.serialize(), {
    skipPreflight: true,
    ...sendOptions,
  });

return await helius.rpc.pollTransactionConfirmation(txSig);
```

----------------------------------------

TITLE: Retrieve Helius Webhook by ID (JavaScript)
DESCRIPTION: Retrieves a specific webhook by its unique ID using the Helius API. This method performs a GET request to the `/v0/webhooks/{webhookID}` endpoint, requiring the webhook's identifier as a parameter. It includes comprehensive error handling for network or API-specific issues.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_3

LANGUAGE: javascript
CODE:
```
getWebhookByID(webhookID) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { data } = yield axios_1.default.get(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`);
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during getWebhookByID: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during getWebhookByID: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Initialize Helius API Client in JavaScript
DESCRIPTION: Demonstrates how to instantiate the `Helius` API client using an API key. This is the foundational step required before making any calls to the Helius API, ensuring all subsequent requests are authenticated.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_1

LANGUAGE: javascript
CODE:
```
class Helius {
    constructor(apiKey) {
        this.apiKey = apiKey;
    }
}
```

----------------------------------------

TITLE: Helius Class Constructor: Initialize API Client
DESCRIPTION: Documents the constructor for the Helius class, which initializes the API client with a provided API key. This is the entry point for interacting with all Helius API methods.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/Helius.html#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Class: Helius
  Constructor: new Helius(apiKey)
    Description: Initializes Helius API client with an API key
    Parameters:
      apiKey:
        Type: string
        Description: API key generated at dev.helius.xyz
```

----------------------------------------

TITLE: Jito Smart Transactions and Bundle Management
DESCRIPTION: Provides methods for integrating Jito tips, creating smart transactions with tips, managing bundle statuses, and sending transaction bundles to the Jito Block Engine.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_10

LANGUAGE: APIDOC
CODE:
```
addTipInstruction(): Adds a tip instruction as the last instruction given the provided instructions.
```

LANGUAGE: APIDOC
CODE:
```
createSmartTransactionWithTip(): Creates a smart transaction with a Jito tip.
```

LANGUAGE: APIDOC
CODE:
```
getBundleStatuses(): Gets the status of the provided bundles.
```

LANGUAGE: APIDOC
CODE:
```
sendJitoBundle(): Sends a bundle of transactions to the Jito Block Engine.
```

LANGUAGE: APIDOC
CODE:
```
sendSmartTransactionWithTip(): Sends a smart transaction as a Jito bundle with a tip.
```

----------------------------------------

TITLE: Helius SDK Available Classes
DESCRIPTION: An index of the main classes provided by the Helius SDK, with links to their respective documentation pages. This section helps users navigate the API.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.html#_snippet_1

LANGUAGE: APIDOC
CODE:
```
Classes:
  - Helius (See Helius.html for details)
```

----------------------------------------

TITLE: Create Webhook API Documentation
DESCRIPTION: API documentation for the `createWebhook` method, detailing its parameters, return type, and potential errors. This method is used to register a new webhook with the Helius platform.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_4

LANGUAGE: APIDOC
CODE:
```
createWebhook(createWebhookRequest)
  description: Creates a new webhook with the provided request
  @param createWebhookRequest: CreateWebhookRequest - the request object containing the webhook information
  @returns Promise<Webhook> - a promise that resolves to the created webhook object
  @throws Error - if there is an error calling the webhooks endpoint or if the response contains an error
```

----------------------------------------

TITLE: Manage Helius Webhooks
DESCRIPTION: Offers comprehensive methods for setting up, editing, and managing webhooks to listen for on-chain Solana events and trigger actions.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_8

LANGUAGE: APIDOC
CODE:
```
createWebhook(): Creates a new webhook with the provided request.
```

LANGUAGE: APIDOC
CODE:
```
editWebhook(): Edits an existing webhook by its ID with the provided request.
```

LANGUAGE: APIDOC
CODE:
```
appendAddressesToWebhook(): Append new addresses to an existing webhook.
```

LANGUAGE: APIDOC
CODE:
```
removeAddressesFromWebhook(): Remove addresses from an existing webhook.
```

LANGUAGE: APIDOC
CODE:
```
deleteWebhook(): Deletes a webhook by its ID.
```

LANGUAGE: APIDOC
CODE:
```
getWebhookByID(): Retrieves a single webhook by its ID.
```

LANGUAGE: APIDOC
CODE:
```
getAllWebhooks(): Retrieves a list of all webhooks.
```

----------------------------------------

TITLE: Initialize Helius API Client
DESCRIPTION: Initializes the Helius API client with the provided API key. This key is essential for authenticating all subsequent API calls to the Helius platform.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_1

LANGUAGE: JavaScript
CODE:
```
constructor(apiKey) {
    this.apiKey = apiKey;
}
```

----------------------------------------

TITLE: Helius API Client Class Definition
DESCRIPTION: Overview of the `Helius` class, the primary interface for the Helius API. This includes its constructor for API key initialization and key methods for webhook management, detailing their parameters, return types, and potential errors.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Helius Class:
  Description: Base level class for interfacing with all Helius API methods.

  Constructor:
    __init__(apiKey: string)
      apiKey: API key generated at dev.helius.xyz

  Methods:
    getAllWebhooks(): Promise<Webhook[]>
      Description: Retrieves a list of all webhooks associated with the current API key.
      Returns: A promise that resolves to an array of webhook objects.
      Throws: Error if there is an error calling the webhooks endpoint or if the response contains an error.

    getWebhookByID(webhookID: string): Promise<Webhook>
      Description: Retrieves a single webhook by its ID, associated with the current API key.
      Parameters:
        webhookID: The ID of the webhook to retrieve.
      Returns: A promise that resolves to a webhook object.
      Throws: Error if there is an error calling the webhooks endpoint or if the response contains an error.
```

----------------------------------------

TITLE: Helius SDK Webhook Management API Reference
DESCRIPTION: Detailed API documentation for the Helius SDK's webhook management functionalities, including method signatures, parameters, return types, and error handling for creating, deleting, editing, and appending addresses to webhooks.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_8

LANGUAGE: APIDOC
CODE:
```
Method: createWebhook
  Description: Creates a new webhook with the provided request.
  Parameters:
    createWebhookRequest: CreateWebhookRequest - The request object containing the webhook information.
  Returns:
    Promise<Webhook> - A promise that resolves to the created webhook object.
  Throws:
    Error - If there is an error calling the webhooks endpoint or if the response contains an error.

Method: deleteWebhook
  Description: Deletes a webhook by its ID.
  Parameters:
    webhookID: string - The ID of the webhook to delete.
  Returns:
    Promise<boolean> - A promise that resolves to true if the webhook was successfully deleted, or false otherwise.
  Throws:
    Error - If there is an error calling the webhooks endpoint or if the response contains an error.

Method: editWebhook
  Description: Edits an existing webhook by its ID with the provided request.
  Parameters:
    webhookID: string - The ID of the webhook to edit.
    editWebhookRequest: EditWebhookRequest - The request object containing the webhook information.
  Returns:
    Promise<Webhook> - A promise that resolves to the edited webhook object.
  Throws:
    Error - If there is an error calling the webhooks endpoint or if the response contains an error.

Method: appendAddressesToWebhook
  Description: Appends an array of addresses to an existing webhook by its ID.
  Parameters:
    webhookID: string - The ID of the webhook to edit.
    newAccountAddresses: string[] - The array of addresses to be added to the webhook.
  Returns:
    Promise<Webhook> - A promise that resolves to the edited webhook object.
  Throws:
    Error - If there is an error calling the webhooks endpoint, if the response contains an error, or if the number of addresses exceeds 10,000.
```

----------------------------------------

TITLE: Mint API
DESCRIPTION: The easiest way to mint compressed NFTs at scale.
Note, this API has been deprecated and the relevant methods will be removed in a future release. Please refer to ZK Compression for all future compression-related work

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_6

LANGUAGE: APIDOC
CODE:
```
mintCompressedNft(): Mint a new compressed NFT.
```

----------------------------------------

TITLE: Helius Class API Reference
DESCRIPTION: Detailed API documentation for the Helius JavaScript SDK's main class, Helius, including its constructor and all available methods for managing webhooks. This section outlines parameters, return types, and potential errors for each function.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/Helius.html#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Class: Helius
  Description: Base level class for interfacing with all Helius API methods.

  Constructor: new Helius(apiKey)
    Description: Initializes Helius API client with an API key.
    Parameters:
      apiKey: string - API key generated at dev.helius.xyz

  Methods:
    appendAddressesToWebhook(webhookID, newAccountAddresses)
      Description: Appends an array of addresses to an existing webhook by its ID.
      Parameters:
        webhookID: string - the ID of the webhook to edit
        newAccountAddresses: Array.<string> - the array of addresses to be added to the webhook
      Throws:
        Error - if there is an error calling the webhooks endpoint, if the response contains an error, or if the number of addresses exceeds 10,000
      Returns:
        Promise.<Webhook> - a promise that resolves to the edited webhook object

    createWebhook(createWebhookRequest)
      Description: Creates a new webhook with the provided request.
      Parameters:
        createWebhookRequest: CreateWebhookRequest - the request object containing the webhook information
      Throws:
        Error - if there is an error calling the webhooks endpoint or if the response contains an error
      Returns:
        Promise.<Webhook> - a promise that resolves to the created webhook object

    deleteWebhook(webhookID)
      Description: Deletes a webhook by its ID.
      Parameters:
        webhookID: string - the ID of the webhook to delete
      Throws:
        Error - if there is an error calling the webhooks endpoint or if the response contains an error
      Returns:
        Promise.<boolean> - a promise that resolves to true if the webhook was successfully deleted, or false otherwise

    editWebhook(webhookID, editWebhookRequest)
      Description: Edits an existing webhook by its ID with the provided request.
      Parameters:
        webhookID: string - the ID of the webhook to edit
        editWebhookRequest: EditWebhookRequest - the request object containing the webhook information
      Throws:
        Error - if there is an error calling the webhooks endpoint or if the response contains an error
      Returns:
        Promise.<Webhook> - a promise that resolves to the edited webhook object

    getAllWebhooks()
      Description: Retrieves a list of all webhooks associated with the current API key.
      Throws:
        Error - if there is an error calling the webhooks endpoint or if the response contains an error
      Returns:
        Promise.<Array.<Webhook>> - a promise that resolves to an array of webhook objects

    getWebhookByID(webhookID)
      Description: Retrieves a single webhook by its ID, associated with the current API key.
      Parameters:
        webhookID: string - the ID of the webhook to retrieve
      Throws:
        Error - if there is an error calling the webhooks endpoint or if the response contains an error
      Returns:
        Promise.<Webhook> - a promise that resolves to a webhook object
```

----------------------------------------

TITLE: DAS API
DESCRIPTION: Comprehensive and performant API for tokens, NFTs, and compressed NFTs on Solana.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_4

LANGUAGE: APIDOC
CODE:
```
getAsset(): Get an asset by its ID.
getAssetBatch(): Get multiple assets by ID (up to 1k).
getSignaturesForAsset(): Get a list of transaction signatures related to a compressed asset.
searchAssets(): Search for assets by a variety of parameters. Very useful for token-gating!
getAssetProof(): Get a Merkle proof for a compressed asset by its ID.
getAssetsByOwner(): Get a list of assets owned by an address. This is the fastest way to get all the NFTs and fungible tokens that are owned by a wallet on Solana.
getAssetsByGroup(): Get a list of assets by a group key and value. This endpoint is very useful for getting the mint list for NFT Collections.
getAssetsByCreator(): Get a list of assets created by an address.
getAssetsByAuthority(): Get a list of assets with a specific authority.
getTokenAccounts(): Get information about all token accounts for a specific mint or a specific owner.
getNftEditions(): Get information about all the edition NFTs for a specific master NFT
```

----------------------------------------

TITLE: Execute Jupiter Swap API Reference
DESCRIPTION: Execute a token swap using Jupiter Exchange with automatic transaction optimizations including priority fees, compute unit calculation, and reliable transaction confirmation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_16

LANGUAGE: APIDOC
CODE:
```
executeJupiterSwap():
  description: Execute a token swap using Jupiter Exchange with automatic transaction optimizations including priority fees, compute unit calculation, and reliable transaction confirmation.
```

----------------------------------------

TITLE: Staking API
DESCRIPTION: The easiest way to stake with Helius programmatically.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_5

LANGUAGE: APIDOC
CODE:
```
createStakeTransaction(): Generate a transaction to create + delegate a new stake account to the Helius validator.
createUnstakeTransaction(): Generate a transaction to deactivate a stake account.
createWithdrawTransaction(): Generate a transaction to withdraw lamports from a stake account (after cooldown).
getStakeInstructions(): Return only the instructions for creating and delegating a stake account.
getUnstakeInstruction(): Return the instruction to deactivate a stake account.
getWithdrawInstruction(): Return the instruction to withdraw lamports from a stake account.
getWithdrawableAmount(): Determine how many lamports are withdrawable (with optional rent-exempt inclusion).
getHeliusStakeAccounts(): Return all stake accounts delegated to the Helius validator for a given wallet.
```

----------------------------------------

TITLE: Execute prettyPrint Utility Function
DESCRIPTION: Demonstrates the invocation of a `prettyPrint` utility function, likely used for formatting output in the documentation generation process.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
prettyPrint();
```

----------------------------------------

TITLE: Helius Class API Reference
DESCRIPTION: This section documents the availability of the 'Helius' class within the SDK. It indicates that detailed API documentation for this class, including its methods, properties, and constructors, can be found by following the provided link to 'Helius.html'. This structure is typical for JSDoc-generated API documentation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_13

LANGUAGE: APIDOC
CODE:
```
Classes:
  - Helius (link: Helius.html)
```

----------------------------------------

TITLE: Helius Smart Transaction Management
DESCRIPTION: Methods for creating, simulating, polling, and sending optimized smart transactions on Solana.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_9

LANGUAGE: APIDOC
CODE:
```
createSmartTransaction(): Creates a smart transaction with the provided configuration.
```

LANGUAGE: APIDOC
CODE:
```
getComputeUnits(): Simulates a transaction to get the total compute units consumed.
```

LANGUAGE: APIDOC
CODE:
```
pollTransactionConfirmation(): Polls a transaction to check whether it has been confirmed.
```

LANGUAGE: APIDOC
CODE:
```
sendSmartTransaction(): Builds and sends an optimized transaction.
```

----------------------------------------

TITLE: Helius Class API Reference
DESCRIPTION: API documentation entry for the `Helius` class, indicating its availability and providing a link to its detailed documentation page. This class serves as a primary interface for the Helius SDK.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_12

LANGUAGE: APIDOC
CODE:
```
Class: Helius
  Documentation Link: Helius.html
```

----------------------------------------

TITLE: Create Webhook using Helius SDK (TypeScript)
DESCRIPTION: This TypeScript function creates a new webhook by sending a POST request to the Helius API. It takes a `CreateWebhookRequest` object as input and returns a Promise that resolves to the created `Webhook` object. Error handling is included for network issues or API errors.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_5

LANGUAGE: TypeScript
CODE:
```
createWebhook(createWebhookRequest) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { data } = yield axios_1.default.post(`${API_URL_V0}/webhooks?api-key=${this.apiKey}`, Object.assign({}, createWebhookRequest));
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during createWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during createWebhook: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Helius Method: Create Webhook
DESCRIPTION: Documents the method for creating a new webhook. It takes a CreateWebhookRequest object as input, returning a promise that resolves to the newly created webhook object or throws an error on API failure.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/Helius.html#_snippet_2

LANGUAGE: APIDOC
CODE:
```
Method: createWebhook(createWebhookRequest)
  Description: Creates a new webhook with the provided request
  Parameters:
    createWebhookRequest:
      Type: CreateWebhookRequest
      Description: the request object containing the webhook information
  Throws:
    Type: Error
    Description: if there is an error calling the webhooks endpoint or if the response contains an error
  Returns:
    Type: Promise.<Webhook>
    Description: a promise that resolves to the created webhook object
```

----------------------------------------

TITLE: Create Webhook with Helius SDK
DESCRIPTION: This function creates a new webhook by sending a POST request to the Helius API. It takes a `CreateWebhookRequest` object as input, which contains the necessary webhook information. The function returns a Promise that resolves to the newly created `Webhook` object upon success, or throws an error if the API call fails or returns an error.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_4

LANGUAGE: typescript
CODE:
```
createWebhook(createWebhookRequest) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { data } = yield axios_1.default.post(`${API_URL_V0}/webhooks?api-key=${this.apiKey}`, Object.assign({}, createWebhookRequest));
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during createWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during createWebhook: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Execute prettyPrint Function
DESCRIPTION: This simple JavaScript code snippet calls the `prettyPrint()` function. This function is typically used in documentation or development environments to format and display output in a more readable way, such as pretty-printing JSON or code. Its exact behavior depends on its definition elsewhere in the codebase.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_14

LANGUAGE: JavaScript
CODE:
```
prettyPrint();
```

----------------------------------------

TITLE: Helius API Client Class Definition
DESCRIPTION: Defines the Helius class, the primary interface for the Helius API. It outlines the constructor and key methods for interacting with Helius services, specifically webhook management.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_0

LANGUAGE: APIDOC
CODE:
```
class Helius {
  /**
   * Initializes Helius API client with an API key
   * @constructor
   * @param apiKey - API key generated at dev.helius.xyz
   */
  constructor(apiKey: string)

  /**
   * Retrieves a list of all webhooks associated with the current API key
   * @returns {Promise<Webhook[]>} a promise that resolves to an array of webhook objects
   * @throws {Error} if there is an error calling the webhooks endpoint or if the response contains an error
   */
  getAllWebhooks(): Promise<Webhook[]>

  /**
   * Retrieves a single webhook by its ID, associated with the current API key
   * @param {string} webhookID - the ID of the webhook to retrieve
   * @returns {Promise<Webhook>} a promise that resolves to a webhook object
   * @throws {Error} if there is an error calling the webhooks endpoint or if the response contains an error
   */
  getWebhookByID(webhookID: string): Promise<Webhook>
}
```

----------------------------------------

TITLE: Execute prettyPrint function
DESCRIPTION: A JavaScript function call to pretty print content, likely used for formatting documentation output, often found in JSDoc generated pages.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.html#_snippet_0

LANGUAGE: JavaScript
CODE:
```
prettyPrint();
```

----------------------------------------

TITLE: Edit Webhook API Documentation
DESCRIPTION: API documentation for the `editWebhook` method, detailing its parameters, return type, and potential errors. This method is used to modify an existing webhook's properties on the Helius platform.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_8

LANGUAGE: APIDOC
CODE:
```
editWebhook(webhookID, editWebhookRequest)
  description: Edits an existing webhook by its ID with the provided request
  @param webhookID: string - the ID of the webhook to edit
  @param editWebhookRequest: EditWebhookRequest - the request object containing the webhook information
  @returns Promise<Webhook> - a promise that resolves to the edited webhook object
  @throws Error - if there is an error calling the webhooks endpoint or if the response contains an error
```

----------------------------------------

TITLE: Reference for common Helius API error codes
DESCRIPTION: A table detailing common error codes returned by the Helius API. Each entry includes the error code, message, and additional information to aid in troubleshooting API issues.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_3

LANGUAGE: APIDOC
CODE:
```
401: Unauthorized - This occurs when an invalid API key is provided or access is restricted due to RPC rules.
429: Too Many Requests - This indicates that the user has exceeded the request limit in a given timeframe or is out of credits.
5XX: Internal Server Error - This is a generic error message for server-side issues. Please contact Helius support for assistance.
```

----------------------------------------

TITLE: Manage NFT Collection Authority and Mintlist
DESCRIPTION: Provides methods to delegate or revoke collection authority for NFTs and retrieve all tokens within an NFT collection.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_7

LANGUAGE: APIDOC
CODE:
```
delegateCollectionAuthority(): Delegates collection authority to a new address.
```

LANGUAGE: APIDOC
CODE:
```
revokeCollectionAuthority(): Revokes collection authority from an address.
```

LANGUAGE: APIDOC
CODE:
```
getMintlist(): Get all the tokens for an NFT collection.
```

----------------------------------------

TITLE: Export Helius Class
DESCRIPTION: This snippet shows the standard TypeScript/JavaScript pattern for exporting the `Helius` class, making it available for import and use in other modules of the SDK.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_10

LANGUAGE: TypeScript
CODE:
```
exports.Helius = Helius;
```

----------------------------------------

TITLE: Call prettyPrint Function
DESCRIPTION: A simple JavaScript function call, likely used for formatting or displaying output in a readable manner, often found in documentation generation or debugging scripts.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_11

LANGUAGE: JavaScript
CODE:
```
prettyPrint();
```

----------------------------------------

TITLE: Append Addresses to Webhook API Documentation
DESCRIPTION: API documentation for the `appendAddressesToWebhook` method, detailing its parameters, return type, and potential errors. This method is used to add new account addresses to an existing webhook on the Helius platform.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_10

LANGUAGE: APIDOC
CODE:
```
appendAddressesToWebhook(webhookID, newAccountAddresses)
  description: Appends an array of addresses to an existing webhook by its ID
  @param webhookID: string - the ID of the webhook to edit
  @param newAccountAddresses: string[] - the array of addresses to be added to the webhook
  @returns Promise<Webhook> - a promise that resolves to the edited webhook object
  @throws Error - if there is an error calling the webhooks endpoint, if the response contains an error, or if the number of addresses exceeds 10,000
```

----------------------------------------

TITLE: Append Addresses to Webhook using Helius SDK (TypeScript)
DESCRIPTION: This TypeScript function appends new account addresses to an existing webhook. It retrieves the webhook, concatenates the new addresses, checks for a 10,000 address limit, and then sends a PUT request to update the webhook. It returns a Promise that resolves to the updated `Webhook` object. Error handling is included for network issues, API errors, or exceeding the address limit.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_11

LANGUAGE: TypeScript
CODE:
```
appendAddressesToWebhook(webhookID, newAccountAddresses) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const webhook = yield this.getWebhookByID(webhookID);
            const accountAddresses = webhook.accountAddresses.concat(newAccountAddresses);
            webhook.accountAddresses = accountAddresses;
            if (accountAddresses.length > 10000) {
                throw new Error(`a single webhook cannot contain more than 10,000 addresses`);
            }
            const { data } = yield axios_1.default.put(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`, Object.assign({}, webhook));
            return data;
        }
```

----------------------------------------

TITLE: Retrieve All Helius Webhooks
DESCRIPTION: Fetches a list of all webhooks associated with the current API key from the Helius API. This method handles potential errors from the API call, including Axios-specific errors, and throws a descriptive error message if the request fails.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_2

LANGUAGE: JavaScript
CODE:
```
getAllWebhooks() {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { data } = yield axios_1.default.get(`${API_URL_V0}/webhooks?api-key=${this.apiKey}`);
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error calling getWebhooks: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error calling getWebhooks: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Delete Webhook API Documentation
DESCRIPTION: API documentation for the `deleteWebhook` method, detailing its parameters, return type, and potential errors. This method is used to remove an existing webhook from the Helius platform.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_6

LANGUAGE: APIDOC
CODE:
```
deleteWebhook(webhookID)
  description: Deletes a webhook by its ID
  @param webhookID: string - the ID of the webhook to delete
  @returns Promise<boolean> - a promise that resolves to true if the webhook was successfully deleted, or false otherwise
  @throws Error - if there is an error calling the webhooks endpoint or if the response contains an error
```

----------------------------------------

TITLE: Retrieve Helius Webhook by ID
DESCRIPTION: Retrieves a specific webhook using its unique ID, associated with the current API key. This method allows for targeted retrieval of webhook configurations and includes robust error handling for API call failures, providing clear error messages.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_3

LANGUAGE: JavaScript
CODE:
```
getWebhookByID(webhookID) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const { data } = yield axios_1.default.get(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`);
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during getWebhookByID: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during getWebhookByID: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Send Transaction API Reference
DESCRIPTION: Wrapper for `sendTransaction` RPC call that includes support for `validatorAcls` parameter.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_14

LANGUAGE: APIDOC
CODE:
```
sendTransaction():
  description: Wrapper for `sendTransaction` RPC call that includes support for `validatorAcls` parameter.
```

----------------------------------------

TITLE: Helius Method: Append Addresses to Webhook
DESCRIPTION: Documents the method to append new account addresses to an existing webhook. It requires the webhook's ID and an array of addresses, returning a promise that resolves to the updated webhook object or throws an error if limits are exceeded or API call fails.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/Helius.html#_snippet_1

LANGUAGE: APIDOC
CODE:
```
Method: appendAddressesToWebhook(webhookID, newAccountAddresses)
  Description: Appends an array of addresses to an existing webhook by its ID
  Parameters:
    webhookID:
      Type: string
      Description: the ID of the webhook to edit
    newAccountAddresses:
      Type: Array.<string>
      Description: the array of addresses to be added to the webhook
  Throws:
    Type: Error
    Description: if there is an error calling the webhooks endpoint, if the response contains an error, or if the number of addresses exceeds 10,000
  Returns:
    Type: Promise.<Webhook>
    Description: a promise that resolves to the edited webhook object
```

----------------------------------------

TITLE: Append Addresses to Webhook with Helius SDK
DESCRIPTION: This function adds new account addresses to an existing webhook. It takes the webhook's ID and an array of new addresses. The function retrieves the webhook, concatenates the new addresses, and validates that the total number of addresses does not exceed 10,000. It then sends a PUT request to update the webhook. A Promise resolving to the updated `Webhook` object is returned, or an error is thrown if the API call fails or the address limit is exceeded.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_7

LANGUAGE: typescript
CODE:
```
appendAddressesToWebhook(webhookID, newAccountAddresses) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const webhook = yield this.getWebhookByID(webhookID);
            const accountAddresses = webhook.accountAddresses.concat(newAccountAddresses);
            webhook.accountAddresses = accountAddresses;
            if (accountAddresses.length > 10000) {
                throw new Error(`a single webhook cannot contain more than 10,000 addresses`);
            }
            const { data } = yield axios_1.default.put(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`, Object.assign({}, webhook));
            return data;
        }
```

----------------------------------------

TITLE: Broadcast Transaction API Reference
DESCRIPTION: Broadcasts a fully signed transaction (object or serialized) and polls for its confirmation.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/README.md#_snippet_15

LANGUAGE: APIDOC
CODE:
```
broadcastTransaction():
  description: Broadcasts a fully signed transaction (object or serialized) and polls for its confirmation.
```

----------------------------------------

TITLE: Handle Axios Errors in Helius SDK
DESCRIPTION: This TypeScript snippet demonstrates robust error handling within the Helius SDK, specifically for network requests made with Axios. It checks if an error is an AxiosError to extract detailed response data, otherwise rethrows a generic error, ensuring informative error messages for debugging.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_9

LANGUAGE: TypeScript
CODE:
```
catch (err) {
    if (axios_1.default.isAxiosError(err)) {
        throw new Error(`error during appendAddressesToWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
    }
    else {
        throw new Error(`error during appendAddressesToWebhook: ${err}`);
    }
}
```

----------------------------------------

TITLE: Edit Existing Webhook with Helius SDK
DESCRIPTION: This function modifies an existing webhook identified by its ID. It takes an `EditWebhookRequest` object containing the updated webhook information. The function first retrieves the current webhook details, then sends a PUT request to the Helius API with the combined original and updated data. It returns a Promise that resolves to the updated `Webhook` object, or throws an error on API call failure.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_6

LANGUAGE: typescript
CODE:
```
editWebhook(webhookID, editWebhookRequest) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const webhook = yield this.getWebhookByID(webhookID);
            const { data } = yield axios_1.default.put(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`, Object.assign(Object.assign({}, webhook), editWebhookRequest));
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during editWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during editWebhook: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Helius Method: Edit Webhook by ID
DESCRIPTION: Documents the method for editing an existing webhook. It requires the webhook's ID and an EditWebhookRequest object, returning a promise that resolves to the updated webhook object or throws an error if the API call fails.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/Helius.html#_snippet_4

LANGUAGE: APIDOC
CODE:
```
Method: editWebhook(webhookID, editWebhookRequest)
  Description: Edits an existing webhook by its ID with the provided request
  Parameters:
    webhookID:
      Type: string
      Description: the ID of the webhook to edit
    editWebhookRequest:
      Type: EditWebhookRequest
      Description: the request object containing the webhook information
  Throws:
    Type: Error
    Description: if there is an error calling the webhooks endpoint or if the response contains an error
  Returns:
    Type: Promise.<Webhook>
    Description: a promise that resolves to the edited webhook object
```

----------------------------------------

TITLE: Edit Webhook by ID using Helius SDK (TypeScript)
DESCRIPTION: This TypeScript function modifies an existing webhook identified by its ID. It first retrieves the current webhook details and then sends a PUT request with the updated `EditWebhookRequest` object to the Helius API. It returns a Promise that resolves to the edited `Webhook` object. Error handling is included for network issues or API errors.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_9

LANGUAGE: TypeScript
CODE:
```
editWebhook(webhookID, editWebhookRequest) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const webhook = yield this.getWebhookByID(webhookID);
            const { data } = yield axios_1.default.put(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`, Object.assign(Object.assign({}, webhook), editWebhookRequest));
            return data;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during editWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during editWebhook: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Handle Axios Error in Helius SDK appendAddressesToWebhook
DESCRIPTION: This TypeScript code snippet demonstrates robust error handling within an asynchronous function, specifically for operations involving `appendAddressesToWebhook`. It checks if the caught error is an Axios error to extract specific response data for more detailed reporting, otherwise, it throws a generic error. This pattern ensures comprehensive error reporting for API calls.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_12

LANGUAGE: TypeScript
CODE:
```
catch (err) {
    if (axios_1.default.isAxiosError(err)) {
        throw new Error(`error during appendAddressesToWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
    }
    else {
        throw new Error(`error during appendAddressesToWebhook: ${err}`);
    }
}
```

----------------------------------------

TITLE: Delete Webhook by ID using Helius SDK (TypeScript)
DESCRIPTION: This TypeScript function deletes an existing webhook using its ID by sending a DELETE request to the Helius API. It returns a Promise that resolves to `true` on successful deletion or `false` otherwise. Error handling is included for network issues or API errors.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/index.js.html#_snippet_7

LANGUAGE: TypeScript
CODE:
```
deleteWebhook(webhookID) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            yield axios_1.default.delete(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`);
            return true;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during deleteWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during deleteWebhook: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Delete Webhook by ID with Helius SDK
DESCRIPTION: This function deletes an existing webhook using its unique identifier. It sends a DELETE request to the Helius API. The function returns a Promise that resolves to `true` if the webhook is successfully deleted, or `false` otherwise. An error is thrown if the API call encounters an issue or the response indicates an error.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/docs/index.js.html#_snippet_5

LANGUAGE: typescript
CODE:
```
deleteWebhook(webhookID) {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        try {
            yield axios_1.default.delete(`${API_URL_V0}/webhooks/${webhookID}?api-key=${this.apiKey}`);
            return true;
        }
        catch (err) {
            if (axios_1.default.isAxiosError(err)) {
                throw new Error(`error during deleteWebhook: ${(_a = err.response) === null || _a === void 0 ? void 0 : _a.data.error}`);
            }
            else {
                throw new Error(`error during deleteWebhook: ${err}`);
            }
        }
    });
}
```

----------------------------------------

TITLE: Helius Method: Delete Webhook by ID
DESCRIPTION: Documents the method to delete an existing webhook using its ID. It returns a promise that resolves to a boolean indicating success or failure, or throws an error if the API call encounters an issue.

SOURCE: https://github.com/helius-labs/helius-sdk/blob/main/out/Helius.html#_snippet_3

LANGUAGE: APIDOC
CODE:
```
Method: deleteWebhook(webhookID)
  Description: Deletes a webhook by its ID
  Parameters:
    webhookID:
      Type: string
      Description: the ID of the webhook to delete
  Throws:
    Type: Error
    Description: if there is an error calling the webhooks endpoint or if the response contains an error
  Returns:
    Type: Promise.<boolean>
    Description: a promise that resolves to true if the webhook was successfully deleted, or false otherwise
```
