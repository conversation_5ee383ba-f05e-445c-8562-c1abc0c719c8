# Local Services Infrastructure (Docker-based)

## 1. Docker Compose Configuration

Create `docker-compose.yml` in project root:

```yaml
version: '3.8'

services:
  # PostgreSQL with TimescaleDB Extension
  postgres:
    image: timescale/timescaledb:2.11.2-pg15
    container_name: solana-trading-postgres
    environment:
      POSTGRES_DB: solana_trading_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d solana_trading_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - solana-trading-network

  # Redis for BullMQ and Caching
  redis:
    image: redis:7.2-alpine
    container_name: solana-trading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - solana-trading-network

  # Redis Commander (Web UI for Redis)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: solana-trading-redis-ui
    hostname: redis-commander
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: "local:redis:6379"
      HTTP_USER: admin
      HTTP_PASSWORD: admin
    depends_on:
      - redis
    networks:
      - solana-trading-network

  # pgAdmin (Web UI for PostgreSQL)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: solana-trading-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - solana-trading-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  solana-trading-network:
    driver: bridge
```

## 2. Start Local Services

```bash