# Core Workflows

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant J as Jupiter
    participant H as Helius
    participant Q as Job Queue
    participant P as Price Monitor
    participant T as Telegram

    Note over U,T: Buy Order with Exit Strategy
    U->>F: Enter token address + amount
    F->>A: GET /api/trades/quote
    A->>J: Request quote with slippage
    J-->>A: Return optimal route + price impact
    A-->>F: Quote data with route visualization
    F-->>U: Display quote + exit strategy form
    
    U->>F: Confirm buy + configure exit strategy
    F->>A: POST /api/trades/buy
    A->>J: Build swap transaction
    J-->>A: Transaction data with MEV protection
    A->>H: Submit transaction with priority fees
    H-->>A: Transaction signature
    A->>Q: Queue position monitoring job
    A-->>F: Transaction initiated response
    F-->>U: "Buy order submitted" notification

    Note over Q,T: Automated Position Monitoring
    Q->>P: Start position price monitoring
    loop Every 30 seconds (Armed mode)
        P->>CMC: Batch price request for active positions
        CMC-->>P: Current price data
        P->>Q: Trigger evaluation job
        Q->>A: Check exit conditions
        alt Exit trigger detected
            Q->>A: Execute sell transaction
            A->>J: Get sell quote
            J-->>A: Sell transaction data
            A->>H: Submit sell transaction
            H-->>A: Sell signature
            A->>T: Send "Take profit executed" alert
            Q->>A: Update position status
        end
    end

    Note over U,T: Manual Position Management
    U->>F: View positions dashboard
    F->>A: GET /api/positions
    A-->>F: Active positions with real-time PnL
    U->>F: Click "Close Position" 
    F->>A: DELETE /api/positions/{id}
    A->>Q: Queue immediate sell job (high priority)
    Q->>A: Execute sell transaction
    A->>T: Send "Manual close executed" alert
```
