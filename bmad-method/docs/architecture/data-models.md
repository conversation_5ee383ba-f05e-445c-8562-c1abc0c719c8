# Data Models

## Position

**Purpose:** Represents an active trading position with associated exit strategies and real-time tracking data.

**Key Attributes:**
- id: string - Unique position identifier
- tokenAddress: string - Solana token mint address
- tokenSymbol: string - Human-readable token symbol
- amountTokens: Decimal - Quantity of tokens purchased
- entryPrice: Decimal - Purchase price per token in USD
- entryTimestamp: DateTime - When position was created
- status: PositionStatus - Current position state (active, closing, closed)
- exitStrategy: ExitStrategy - Attached automation rules

### TypeScript Interface

```typescript
interface Position {
  id: string;
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  amountTokens: Decimal;
  entryPrice: Decimal;
  entryTimestamp: Date;
  status: 'active' | 'closing' | 'closed';
  exitStrategy?: ExitStrategy;
  transactions: Transaction[];
  currentPrice?: Decimal;
  currentValue?: Decimal;
  pnlUsd?: Decimal;
  pnlPercent?: Decimal;
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Has many: Transaction records (buy, sell, partial exits)
- Has one: ExitStrategy (optional, for automated management)
- Belongs to: User (implied single-user system)

## ExitStrategy

**Purpose:** Defines automated exit rules including multi-tier take profits, stop losses, and trailing stops for position management.

**Key Attributes:**
- id: string - Unique strategy identifier
- positionId: string - Associated position
- takeProfitTiers: TakeProfitTier[] - Multiple exit levels
- stopLoss: StopLoss - Protection against losses
- trailingStop: TrailingStop - Dynamic stop adjustment
- moonBag: MoonBag - Small position retention
- isActive: boolean - Whether automation is enabled

### TypeScript Interface

```typescript
interface ExitStrategy {
  id: string;
  positionId: string;
  takeProfitTiers: TakeProfitTier[];
  stopLoss?: StopLoss;
  trailingStop?: TrailingStop;
  moonBag?: MoonBag;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface TakeProfitTier {
  id: string;
  tierNumber: number;
  targetPrice: Decimal;
  percentageToSell: number; // 1-100
  status: 'pending' | 'triggered' | 'executed';
}

interface StopLoss {
  triggerPrice: Decimal;
  percentageToSell: number; // typically 100
  status: 'active' | 'triggered' | 'executed';
}

interface TrailingStop {
  trailDistance: Decimal; // USD amount to trail by
  highestPrice: Decimal; // tracks highest price reached
  currentStopPrice: Decimal; // current stop loss level
  isActive: boolean;
}
```

### Relationships
- Belongs to: Position (one-to-one relationship)
- Has many: Exit executions tracked in Transaction records

## Transaction

**Purpose:** Immutable record of all blockchain transactions including buys, sells, and fees for audit trail and analytics.

**Key Attributes:**
- id: string - Unique transaction identifier
- positionId: string - Associated position
- type: TransactionType - Buy, sell, or fee transaction
- signature: string - Solana transaction signature
- amountTokens: Decimal - Quantity traded
- pricePerToken: Decimal - Execution price
- totalUsd: Decimal - Total USD value
- fees: TransactionFees - Breakdown of all fees paid
- status: TransactionStatus - Transaction state
- executedAt: DateTime - Blockchain confirmation time

### TypeScript Interface

```typescript
interface Transaction {
  id: string;
  positionId: string;
  type: 'buy' | 'sell' | 'fee';
  signature: string;
  amountTokens: Decimal;
  pricePerToken: Decimal;
  totalUsd: Decimal;
  fees: {
    networkFee: Decimal; // SOL network fee
    priorityFee: Decimal; // MEV protection fee
    jupiterFee: Decimal; // Platform fee
  };
  status: 'pending' | 'confirmed' | 'failed';
  blockNumber?: number;
  executedAt: Date;
  createdAt: Date;
}
```

### Relationships
- Belongs to: Position (many transactions per position)
- References: ExitStrategy triggers (for sell transactions)

## WatchlistItem

**Purpose:** Tracks tokens of interest for research and monitoring before trading decisions are made.

**Key Attributes:**
- id: string - Unique item identifier
- tokenAddress: string - Solana mint address
- customName: string - User-defined token name
- notes: string - Research notes and observations
- isPinned: boolean - Priority flag for enhanced monitoring
- addedAt: DateTime - When added to watchlist
- lastViewedAt: DateTime - User engagement tracking

### TypeScript Interface

```typescript
interface WatchlistItem {
  id: string;
  tokenAddress: string;
  tokenSymbol?: string; // fetched from metadata
  tokenName?: string; // fetched from metadata
  customName?: string; // user override
  notes?: string;
  isPinned: boolean;
  addedAt: Date;
  lastViewedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### Relationships
- Independent entity (no foreign keys)
- Conceptually related to Position creation workflows

## PriceSnapshot

**Purpose:** Time-series storage of token price data for monitoring, analytics, and trigger detection using TimescaleDB optimization.

**Key Attributes:**
- timestamp: DateTime - Price observation time (TimescaleDB partition key)
- tokenAddress: string - Token identifier
- priceUsd: Decimal - Current USD price
- volume24h: Decimal - 24-hour trading volume
- marketCap: Decimal - Current market capitalization
- priceChange1h: Decimal - 1-hour price change percentage
- priceChange24h: Decimal - 24-hour price change percentage
- source: string - Data provider (CMC, Jupiter, etc.)

### TypeScript Interface

```typescript
interface PriceSnapshot {
  timestamp: Date; // TimescaleDB hypertable partition key
  tokenAddress: string;
  priceUsd: Decimal;
  volume24h?: Decimal;
  marketCap?: Decimal;
  priceChange1h?: Decimal;
  priceChange24h?: Decimal;
  liquidity?: Decimal;
  fdv?: Decimal; // Fully diluted valuation
  source: 'cmc' | 'jupiter' | 'helius';
}
```

### Relationships
- Time-series data indexed by timestamp and tokenAddress
- Used by Position monitoring and WatchlistItem price updates
