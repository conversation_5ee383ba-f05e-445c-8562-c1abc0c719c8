# External APIs

## Jupiter Aggregator API

- **Purpose:** DEX aggregation for optimal token swap routing and quote generation
- **Documentation:** https://docs.jup.ag/apis/swap-api
- **Base URL(s):** https://quote-api.jup.ag/v6
- **Authentication:** No API key required (rate limited)
- **Rate Limits:** 600 requests/minute per IP

**Key Endpoints Used:**
- `GET /quote` - Get swap quotes with route optimization
- `POST /swap` - Generate swap transactions with MEV protection

**Integration Notes:** Essential for buy/sell execution. Supports priority fees and compute unit optimization for MEV protection. Route data used for price impact warnings.

## Helius RPC API

- **Purpose:** High-performance Solana RPC with enhanced transaction broadcasting and webhook capabilities
- **Documentation:** https://docs.helius.xyz/
- **Base URL(s):** https://rpc.helius.xyz/?api-key={key}
- **Authentication:** API key in URL parameter
- **Rate Limits:** Free tier: 100 req/sec, 150k req/day

**Key Endpoints Used:**
- `POST /` - Standard Solana RPC methods (sendTransaction, getTransaction, etc.)
- `POST /v0/transactions` - Enhanced transaction submission with priority handling

**Integration Notes:** Primary blockchain interface for transaction submission and confirmation. Webhook integration for real-time transaction monitoring (future enhancement).

## CoinMarketCap DEX API

- **Purpose:** Real-time price data and market metrics for token monitoring and trigger detection
- **Documentation:** https://coinmarketcap.com/api/documentation/v1/
- **Base URL(s):** https://pro-api.coinmarketcap.com/v2/
- **Authentication:** API key in X-CMC_PRO_API_KEY header
- **Rate Limits:** Basic plan: 10k calls/month, 333 calls/day

**Key Endpoints Used:**
- `GET /cryptocurrency/quotes/latest` - Batch price quotes (up to 100 tokens per call)

**Integration Notes:** Batch fetching optimizes API usage for multiple position monitoring. Price data cached in Redis for polling efficiency.

## Telegram Bot API

- **Purpose:** Real-time notifications for trading events, alerts, and system status updates
- **Documentation:** https://core.telegram.org/bots/api
- **Base URL(s):** https://api.telegram.org/bot{token}/
- **Authentication:** Bot token in URL path
- **Rate Limits:** 30 messages/second per bot

**Key Endpoints Used:**
- `POST /sendMessage` - Send text notifications with formatting
- `POST /sendPhoto` - Send charts and visual updates (future enhancement)

**Integration Notes:** Critical for mobile notifications and hands-off trading monitoring. Messages queued through BullMQ for reliability and rate limit compliance.
