# Testing Strategy

## Testing Pyramid

```
              E2E Tests
             /        \
        Integration Tests
           /            \
      Frontend Unit  Backend Unit
```

## Test Organization

### Frontend Tests

```
apps/web/tests/
├── components/           # Component unit tests
│   ├── trading/
│   │   ├── TradingPanel.test.tsx
│   │   ├── QuoteDisplay.test.tsx
│   │   └── ExitStrategyForm.test.tsx
│   ├── positions/
│   │   ├── PositionCard.test.tsx
│   │   ├── PositionDashboard.test.tsx
│   │   └── ManualCloseButton.test.tsx
│   └── watchlist/
│       ├── WatchlistTable.test.tsx
│       ├── AddTokenDialog.test.tsx
│       └── BulkAddDialog.test.tsx
├── hooks/                # Custom hook tests
│   ├── useTrading.test.ts
│   ├── usePositions.test.ts
│   ├── useWatchlist.test.ts
│   └── useRealTimePrice.test.ts
├── services/             # API service tests
│   ├── trading.test.ts
│   ├── positions.test.ts
│   └── watchlist.test.ts
├── stores/               # State management tests
│   ├── tradingStore.test.ts
│   ├── positionStore.test.ts
│   └── watchlistStore.test.ts
├── utils/                # Utility function tests
│   ├── formatters.test.ts
│   ├── validators.test.ts
│   └── calculations.test.ts
└── setup.ts              # Test environment setup
```

### Backend Tests

```
apps/api/tests/
├── unit/                 # Unit tests
│   ├── services/
│   │   ├── TradingService.test.ts
│   │   ├── ExitStrategyService.test.ts
│   │   ├── PriceMonitorService.test.ts
│   │   └── NotificationService.test.ts
│   ├── repositories/
│   │   ├── PositionRepository.test.ts
│   │   ├── TransactionRepository.test.ts
│   │   └── WatchlistRepository.test.ts
│   ├── controllers/
│   │   ├── TradeController.test.ts
│   │   ├── PositionController.test.ts
│   │   └── WatchlistController.test.ts
│   └── utils/
│       ├── calculations.test.ts
│       ├── validators.test.ts
│       └── formatters.test.ts
├── integration/          # Integration tests
│   ├── api/
│   │   ├── trades.test.ts
│   │   ├── positions.test.ts
│   │   ├── watchlist.test.ts
│   │   └── health.test.ts
│   ├── jobs/
│   │   ├── priceMonitor.test.ts
│   │   ├── exitExecution.test.ts
│   │   └── notifications.test.ts
│   └── external-apis/
│       ├── jupiter.test.ts
│       ├── helius.test.ts
│       └── coinmarketcap.test.ts
├── fixtures/             # Test data
│   ├── positions.json
│   ├── transactions.json
│   ├── watchlist.json
│   └── api-responses.json
└── setup.ts              # Test environment setup
```

### E2E Tests

```
tests/e2e/
├── trading/              # Trading workflow tests
│   ├── buy-flow.spec.ts
│   ├── sell-flow.spec.ts
│   ├── quote-accuracy.spec.ts
│   └── exit-strategy.spec.ts
├── positions/            # Position management tests
│   ├── position-monitoring.spec.ts
│   ├── manual-close.spec.ts
│   ├── strategy-modification.spec.ts
│   └── emergency-close.spec.ts
├── watchlist/            # Watchlist functionality tests
│   ├── add-tokens.spec.ts
│   ├── bulk-operations.spec.ts
│   ├── price-updates.spec.ts
│   └── integration-flow.spec.ts
├── system/               # System-level tests
│   ├── health-checks.spec.ts
│   ├── error-handling.spec.ts
│   ├── rate-limiting.spec.ts
│   └── performance.spec.ts
└── utils/                # Test utilities
    ├── test-helpers.ts
    ├── mock-data.ts
    └── page-objects/
```

## Test Examples

### Frontend Component Test

```typescript
// apps/web/tests/components/positions/PositionCard.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { PositionCard } from '../../../src/components/positions/PositionCard';
import { mockPosition } from '../../fixtures/positions';

const mockOnClose = vi.fn();
const mockOnModifyStrategy = vi.fn();

describe('PositionCard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('displays position information correctly', () => {
    const position = mockPosition({
      tokenSymbol: 'BONK',
      amountTokens: '1000000',
      entryPrice: '0.000015',
    });

    render(
      <PositionCard
        position={position}
        currentPrice={0.000018}
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    expect(screen.getByText('BONK')).toBeInTheDocument();
    expect(screen.getByText('1,000,000 tokens')).toBeInTheDocument();
    expect(screen.getByText('+20.00%')).toBeInTheDocument();
    expect(screen.getByText('$3.00')).toBeInTheDocument();
  });

  it('calculates PnL correctly for profitable position', () => {
    const position = mockPosition({
      amountTokens: '100000',
      entryPrice: '0.0001',
    });

    render(
      <PositionCard
        position={position}
        currentPrice={0.00015} // 50% gain
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    expect(screen.getByText('+50.00%')).toBeInTheDocument();
    expect(screen.getByText('$5.00')).toBeInTheDocument();
  });

  it('handles close position action', async () => {
    const position = mockPosition();

    render(
      <PositionCard
        position={position}
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    const closeButton = screen.getByText('Close Position');
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalledWith(position.id);
    });
  });

  it('handles modify strategy action', async () => {
    const position = mockPosition();

    render(
      <PositionCard
        position={position}
        onClose={mockOnClose}
        onModifyStrategy={mockOnModifyStrategy}
      />
    );

    const modifyButton = screen.getByText('Modify Strategy');
    fireEvent.click(modifyButton);

    await waitFor(() => {
      expect(mockOnModifyStrategy).toHaveBeenCalledWith(position.id);
    });
  });
});
```

### Backend API Test

```typescript
// apps/api/tests/integration/api/trades.test.ts
import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { TestServer } from '../../utils/test-server';
import { createMockPosition } from '../../fixtures/positions';
import { mockJupiterQuote } from '../../fixtures/external-api-responses';

describe('Trades API', () => {
  let server: TestServer;

  beforeAll(async () => {
    server = new TestServer();
    await server.start();
  });

  afterAll(async () => {
    await server.stop();
  });

  beforeEach(async () => {
    await server.resetDatabase();
  });

  describe('POST /api/trades/quote', () => {
    it('returns valid quote for legitimate token', async () => {
      // Mock Jupiter API response
      server.mockExternalApi('jupiter', '/v6/quote', mockJupiterQuote);

      const response = await server.request
        .post('/api/trades/quote')
        .send({
          tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
          amountUsd: 100,
          slippageBps: 300,
        })
        .expect(200);

      expect(response.body).toMatchObject({
        inputMint: 'So11111111111111111111111111111111111111112', // SOL
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        outputAmount: expect.any(String),
        priceImpact: expect.any(String),
        route: expect.any(Array),
      });
    });

    it('validates input parameters', async () => {
      const response = await server.request
        .post('/api/trades/quote')
        .send({
          tokenAddress: 'invalid-address',
          amountUsd: -10, // Invalid amount
        })
        .expect(400);

      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.details).toContain('tokenAddress');
      expect(response.body.error.details).toContain('amountUsd');
    });

    it('handles external API failures gracefully', async () => {
      // Mock Jupiter API failure
      server.mockExternalApiError('jupiter', '/v6/quote', 503);

      const response = await server.request
        .post('/api/trades/quote')
        .send({
          tokenAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
          amountUsd: 100,
          slippageBps: 300,
        })
        .expect(503);

      expect(response.body.error.code).toBe('EXTERNAL_API_ERROR');
      expect(response.body.error.message).toContain('Jupiter');
    });
  });

  describe('POST /api/trades/buy', () => {
    it('executes buy order with exit strategy', async () => {
      // Mock Jupiter swap response
      server.mockExternalApi('jupiter', '/v6/swap', {
        swapTransaction: 'base64-encoded-transaction',
      });

      // Mock Helius transaction submission
      server.mockExternalApi('helius', '/', {
        result: '5VERSomeTxSignatureHere1234567890',
      });

      const exitStrategy = {
        takeProfitTiers: [
          { targetPrice: '0.0002', percentageToSell: 50 },
          { targetPrice: '0.0003', percentageToSell: 50 },
        ],
        stopLoss: { triggerPrice: '0.00008', percentageToSell: 100 },
      };

      const response = await server.request
        .post('/api/trades/buy')
        .send({
          tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', // BONK
          amountUsd: 50,
          slippageBps: 500,
          exitStrategy,
        })
        .expect(200);

      expect(response.body).toMatchObject({
        signature: expect.any(String),
        status: 'initiated',
        positionId: expect.any(String),
      });

      // Verify position was created in database
      const position = await server.db.position.findFirst({
        where: { id: response.body.positionId },
        include: { exitStrategy: true },
      });

      expect(position).toBeTruthy();
      expect(position?.exitStrategy).toBeTruthy();
    });
  });
});
```

### E2E Test

```typescript
// tests/e2e/trading/buy-flow.spec.ts
import { test, expect } from '@playwright/test';
import { TestHelper } from '../utils/test-helper';

test.describe('Buy Flow', () => {
  let helper: TestHelper;

  test.beforeEach(async ({ page }) => {
    helper = new TestHelper(page);
    await helper.login();
    await helper.navigateToTrading();
  });

  test('complete buy flow with exit strategy', async ({ page }) => {
    // Enter token address
    await page.fill('[data-testid="token-address-input"]', 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
    await page.fill('[data-testid="amount-input"]', '100');

    // Wait for quote to load
    await page.waitForSelector('[data-testid="quote-display"]');
    
    // Verify quote information
    const outputAmount = await page.textContent('[data-testid="output-amount"]');
    expect(outputAmount).toContain('BONK');

    const priceImpact = await page.textContent('[data-testid="price-impact"]');
    expect(priceImpact).toMatch(/\d+\.\d{2}%/);

    // Configure exit strategy
    await page.click('[data-testid="exit-strategy-tab"]');
    
    // Set take profit
    await page.fill('[data-testid="tp-tier-1-price"]', '0.000020');
    await page.fill('[data-testid="tp-tier-1-percent"]', '50');
    
    await page.click('[data-testid="add-tier-button"]');
    await page.fill('[data-testid="tp-tier-2-price"]', '0.000030');
    await page.fill('[data-testid="tp-tier-2-percent"]', '50');

    // Set stop loss
    await page.fill('[data-testid="stop-loss-price"]', '0.000010');

    // Execute buy
    await page.click('[data-testid="execute-buy-button"]');

    // Confirm transaction
    await page.click('[data-testid="confirm-transaction-button"]');

    // Wait for transaction confirmation
    await page.waitForSelector('[data-testid="transaction-success"]');

    // Verify redirect to positions dashboard
    await expect(page).toHaveURL(/\/positions/);

    // Verify position appears in dashboard
    await page.waitForSelector('[data-testid="position-card"]');
    
    const positionCard = page.locator('[data-testid="position-card"]').first();
    await expect(positionCard.locator('[data-testid="token-symbol"]')).toHaveText('BONK');
    await expect(positionCard.locator('[data-testid="position-status"]')).toHaveText('Active');
  });

  test('handles insufficient balance error', async ({ page }) => {
    await helper.setMockBalance('0'); // No SOL balance

    await page.fill('[data-testid="token-address-input"]', 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
    await page.fill('[data-testid="amount-input"]', '1000'); // Large amount

    await page.click('[data-testid="execute-buy-button"]');

    // Verify error message
    await expect(page.locator('[data-testid="error-message"]')).toHaveText(/Insufficient SOL balance/);
    
    // Verify buy button is disabled
    await expect(page.locator('[data-testid="execute-buy-button"]')).toBeDisabled();
  });
});
```
