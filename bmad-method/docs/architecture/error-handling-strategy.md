# Error Handling Strategy

## Error Flow

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as API
    participant S as Service
    participant E as External API
    participant L as Logger

    Note over F,L: Error Handling Flow
    F->>A: API Request
    A->>S: Business Logic Call
    S->>E: External API Call
    
    alt External API Error
        E-->>S: API Error Response
        S->>L: Log external API error
        S->>S: Apply circuit breaker logic
        alt Circuit Open
            S-->>A: ServiceUnavailableError
        else Circuit Closed
            S->>S: Retry with backoff
            S->>E: Retry external call
            E-->>S: Success/Failure
        end
    end
    
    alt Service Error
        S-->>A: BusinessLogicError
        A->>L: Log service error with context
        A->>A: Transform to API error format
    end
    
    alt Database Error
        S-->>A: DatabaseError
        A->>L: Log database error (critical)
        A->>A: Transform to generic error (no DB details exposed)
    end
    
    A-->>F: Standardized Error Response
    F->>F: Display user-friendly message
    F->>L: Log frontend error (optional)
```

## Error Response Format

```typescript
interface ApiError {
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    requestId: string;
  };
}
```

## Frontend Error Handling

```typescript
// services/error-handler.ts
export class ErrorHandler {
  static handleApiError(error: unknown): UserFriendlyError {
    if (error instanceof ApiError) {
      return this.transformApiError(error);
    }
    
    if (error instanceof NetworkError) {
      return {
        message: 'Network connection error. Please check your internet connection.',
        code: 'NETWORK_ERROR',
        severity: 'warning',
        retryable: true,
      };
    }
    
    // Log unexpected errors
    console.error('Unexpected error:', error);
    
    return {
      message: 'An unexpected error occurred. Please try again.',
      code: 'UNKNOWN_ERROR',
      severity: 'error',
      retryable: false,
    };
  }
  
  private static transformApiError(apiError: ApiError): UserFriendlyError {
    const { code, message, details } = apiError.error;
    
    // Map API error codes to user-friendly messages
    const errorMap: Record<string, string> = {
      'INSUFFICIENT_BALANCE': 'You don\'t have enough SOL to complete this transaction.',
      'INVALID_TOKEN_ADDRESS': 'The token address you entered is not valid.',
      'PRICE_IMPACT_TOO_HIGH': 'Price impact is too high. Try reducing the amount or increasing slippage.',
      'EXTERNAL_API_ERROR': 'Trading services are temporarily unavailable. Please try again.',
      'RATE_LIMIT_EXCEEDED': 'Too many requests. Please wait a moment and try again.',
      'POSITION_NOT_FOUND': 'The position you\'re trying to access was not found.',
      'STRATEGY_VALIDATION_ERROR': 'Exit strategy configuration is invalid. Please check your settings.',
    };
    
    return {
      message: errorMap[code] || message,
      code,
      severity: this.getSeverity(code),
      retryable: this.isRetryable(code),
      details,
    };
  }
  
  private static getSeverity(code: string): 'info' | 'warning' | 'error' | 'critical' {
    const criticalCodes = ['DATABASE_ERROR', 'SYSTEM_FAILURE'];
    const warningCodes = ['EXTERNAL_API_ERROR', 'RATE_LIMIT_EXCEEDED'];
    const infoCodes = ['VALIDATION_ERROR', 'POSITION_NOT_FOUND'];
    
    if (criticalCodes.includes(code)) return 'critical';
    if (warningCodes.includes(code)) return 'warning';
    if (infoCodes.includes(code)) return 'info';
    return 'error';
  }
  
  private static isRetryable(code: string): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'EXTERNAL_API_ERROR',
      'RATE_LIMIT_EXCEEDED',
      'TEMPORARY_UNAVAILABLE',
    ];
    return retryableCodes.includes(code);
  }
}

// hooks/useErrorHandler.ts
export function useErrorHandler() {
  const showNotification = useNotificationStore(state => state.addNotification);
  
  const handleError = useCallback((error: unknown, context?: string) => {
    const friendlyError = ErrorHandler.handleApiError(error);
    
    showNotification({
      type: friendlyError.severity,
      title: context ? `${context} Error` : 'Error',
      message: friendlyError.message,
      action: friendlyError.retryable ? {
        label: 'Retry',
        onClick: () => window.location.reload(),
      } : undefined,
    });
    
    // Log to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      logError(error, context);
    }
  }, [showNotification]);
  
  return { handleError };
}
```

## Backend Error Handling

```typescript
// middleware/errorHandler.ts
export class GlobalErrorHandler {
  static handle(
    error: Error,
    req: Request,
    res: Response,
    next: NextFunction
  ): void {
    const requestId = req.headers['x-request-id'] as string || generateRequestId();
    const timestamp = new Date().toISOString();
    
    // Log error with context
    logger.error('Request failed', {
      requestId,
      method: req.method,
      path: req.path,
      error: error.message,
      stack: error.stack,
      userId: req.user?.id,
    });
    
    if (error instanceof ValidationError) {
      res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Request validation failed',
          details: error.details,
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    if (error instanceof BusinessLogicError) {
      res.status(400).json({
        error: {
          code: error.code,
          message: error.message,
          details: error.details,
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    if (error instanceof ExternalApiError) {
      const statusCode = error.isTemporary ? 503 : 502;
      res.status(statusCode).json({
        error: {
          code: 'EXTERNAL_API_ERROR',
          message: 'External service temporarily unavailable',
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    if (error instanceof DatabaseError) {
      // Never expose database details to client
      res.status(500).json({
        error: {
          code: 'DATABASE_ERROR',
          message: 'Internal server error',
          timestamp,
          requestId,
        },
      });
      return;
    }
    
    // Generic error fallback
    res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        timestamp,
        requestId,
      },
    });
  }
}

// Custom error classes
export class BusinessLogicError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'BusinessLogicError';
  }
}

export class ExternalApiError extends Error {
  constructor(
    message: string,
    public service: string,
    public isTemporary: boolean = true,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ExternalApiError';
  }
}

// Circuit breaker for external APIs
export class CircuitBreaker {
  private failures = 0;
  private lastFailure = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private failureThreshold: number = 5,
    private timeout: number = 60000 // 1 minute
  ) {}
  
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailure < this.timeout) {
        throw new ExternalApiError('Circuit breaker is open', 'circuit-breaker');
      }
      this.state = 'half-open';
    }
    
    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    this.failures = 0;
    this.state = 'closed';
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailure = Date.now();
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'open';
    }
  }
}
```
