# High Level Architecture

## Technical Summary

The Solana Trading App follows a modern monorepo architecture with Next.js frontend and Express.js backend, deployed across Vercel and Railway respectively. The system integrates Jupiter Aggregator for optimal DEX routing, Helius for low-latency blockchain access, and implements BullMQ job queues for reliable trading automation. Key integration points include shared TypeScript interfaces, RESTful APIs for frontend-backend communication, and real-time price monitoring through batched CMC API calls. This architecture achieves sub-5-second execution requirements through optimized polling strategies, MEV-protected transactions, and priority-based job processing within Helius rate limits.

## Platform and Infrastructure Choice

**Platform:** Vercel + Railway + TimescaleDB Cloud
**Key Services:** 
- Frontend: Vercel (Next.js hosting, edge functions, automatic deployments)
- Backend: Railway (Node.js, PostgreSQL with TimescaleDB, Redis hosting)
- Database: Railway PostgreSQL with TimescaleDB extension for time-series price data
- Cache/Queue: Railway Redis for BullMQ and caching
- Monitoring: Railway metrics + Vercel analytics

**Deployment Host and Regions:** 
- Vercel: Global edge network with primary regions in US-East, US-West
- Railway: US-West region for low latency to Solana RPC endpoints
- TimescaleDB Cloud: Co-located with Railway for optimal database performance

## Repository Structure

**Structure:** Monorepo with npm workspaces
**Monorepo Tool:** npm workspaces (lightweight, built-in dependency management)
**Package Organization:** 
- `/apps` - Frontend (web) and backend (api) applications
- `/packages` - Shared utilities (shared types, UI components, config)
- Clear separation between client and server code with shared interfaces

## High Level Architecture Diagram

```mermaid
graph TB
    User[User] --> Web[Next.js Frontend<br/>Vercel]
    Web --> API[Express API<br/>Railway]
    API --> Queue[BullMQ Jobs<br/>Redis]
    API --> DB[(PostgreSQL<br/>TimescaleDB)]
    
    Queue --> Jupiter[Jupiter Aggregator<br/>DEX Routing]
    Queue --> Helius[Helius RPC<br/>Blockchain Access]
    Queue --> CMC[CoinMarketCap<br/>Price Data]
    Queue --> Telegram[Telegram Bot<br/>Notifications]
    
    API --> Cache[(Redis Cache<br/>Price Data)]
    Web --> CDN[Vercel Edge<br/>Static Assets]
    
    Jupiter --> Solana[Solana Blockchain]
    Helius --> Solana
    
    style User fill:#e1f5fe
    style Web fill:#f3e5f5
    style API fill:#e8f5e8
    style Queue fill:#fff3e0
    style DB fill:#fce4ec
    style Solana fill:#f1f8e9
```

## Architectural Patterns

- **Monorepo Architecture:** Single repository with multiple packages for shared code and simplified dependency management - _Rationale:_ Enables type safety across frontend/backend boundaries and simplifies development workflow
- **Job Queue Pattern:** BullMQ handles all critical trading operations with retry logic and priority scheduling - _Rationale:_ Ensures reliable execution of time-sensitive trades within API rate limits
- **Repository Pattern:** Abstract data access layer with TypeScript interfaces - _Rationale:_ Enables testing and future database migration flexibility while maintaining type safety
- **State-Aware Polling:** Dynamic polling intervals based on position status and user engagement - _Rationale:_ Optimizes API usage while maintaining responsiveness for active trading
- **Circuit Breaker Pattern:** Graceful degradation when external APIs fail or hit rate limits - _Rationale:_ Maintains system stability and user experience during external service disruptions
