# Monitoring and Observability

## Monitoring Stack

- **Frontend Monitoring:** Vercel Analytics for Core Web Vitals, Sentry for JavaScript error tracking, custom metrics for trading actions
- **Backend Monitoring:** Railway built-in metrics for infrastructure, Pino structured logging, Bull Board for job queue monitoring
- **Error Tracking:** Sentry for both frontend and backend error aggregation with trading context
- **Performance Monitoring:** Custom metrics collection for trading execution times, API response times, and database query performance

## Key Metrics

**Frontend Metrics:**
- Core Web Vitals (LCP < 2.5s, FID < 100ms, CLS < 0.1)
- JavaScript errors with trading context (position ID, token address)
- API response times for critical paths (quote fetch < 1s, buy execution < 3s)
- User interactions (clicks, form submissions, successful trades)

**Backend Metrics:**
- Request rate (target: < 1000 req/min steady state)
- Error rate (target: < 1% for trading operations)
- Response time (95th percentile < 500ms for API calls)
- Database query performance (slow query threshold: > 100ms)
- Job queue metrics (processing time, failure rate, backlog size)
- External API health (<PERSON>, Helius, CMC response times and error rates)
- Trading-specific metrics (execution time < 5s, success rate > 95%, slippage tracking)

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"content":"Execute create-doc task with fullstack-architecture template","status":"completed","id":"1"},{"content":"Process all architecture sections with user interaction","status":"completed","id":"2"},{"content":"Apply user tech stack refinements (Vitest, session auth, TimescaleDB)","status":"completed","id":"3"},{"content":"Update polling intervals (Armed mode: 30s)","status":"completed","id":"4"},{"content":"Update PRD with watchlist requirements","status":"completed","id":"5"},{"content":"Complete remaining architecture sections","status":"completed","id":"6"},{"content":"Finalize with monitoring section","status":"completed","id":"7"},{"content":"Generate final architecture document","status":"completed","id":"8"}]