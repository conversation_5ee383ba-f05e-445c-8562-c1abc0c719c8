# Components

## Trading Engine

**Responsibility:** Core trading operations including quote fetching, transaction building, and execution coordination with external APIs.

**Key Interfaces:**
- `getTradingQuote(tokenAddress, amountUsd, slippageBps)` - Jupiter integration for optimal routing
- `executeBuyOrder(quote, exitStrategy)` - MEV-protected transaction execution
- `executeSellOrder(position, amount?, reason)` - Automated and manual position closure

**Dependencies:** Jupiter Aggregator API, Helius RPC, Wallet Service, Database repositories

**Technology Stack:** Node.js/TypeScript with @solana/web3.js, Jupiter SDK, Express.js route handlers, Prisma for position persistence

## Exit Strategy Engine

**Responsibility:** Automated exit management including trigger detection, priority handling, and execution coordination through job queues.

**Key Interfaces:**
- `attachExitStrategy(positionId, strategy)` - Associate automation with position
- `evaluateExitTriggers(positions[])` - Batch trigger detection from price monitoring
- `executeExitTrigger(trigger, position)` - Priority-based execution scheduling

**Dependencies:** Price Monitor, Trading Engine, BullMQ job processing, Database for trigger state

**Technology Stack:** BullMQ workers, Redis job storage, Prisma repository pattern, TypeScript for type-safe trigger logic

## Price Monitor

**Responsibility:** Real-time price data collection and distribution with state-aware polling optimization for efficiency and trigger detection.

**Key Interfaces:**
- `startPolling()` - Initialize monitoring with dynamic intervals
- `getPriceSnapshot(tokenAddresses[])` - Batch price fetching
- `subscribeToUpdates(callback)` - Real-time price change notifications

**Dependencies:** CoinMarketCap DEX API, Redis caching, TimescaleDB price storage, polling strategy configuration

**Technology Stack:** Node.js intervals with state management, CMC API integration, Redis caching, TimescaleDB hypertables for time-series data

## Position Manager

**Responsibility:** Position lifecycle management including creation, updates, analytics, and historical tracking with real-time PnL calculations.

**Key Interfaces:**
- `createPosition(transaction, exitStrategy)` - New position initialization
- `updatePosition(id, updates)` - Position modification and exit strategy changes
- `calculatePnL(position, currentPrice)` - Real-time profit/loss computation

**Dependencies:** Database repositories, Price Monitor for current prices, Transaction records for cost basis calculation

**Technology Stack:** TypeScript business logic, Prisma ORM, PostgreSQL with TimescaleDB, Decimal.js for financial calculations

## Watchlist Manager

**Responsibility:** Token discovery and monitoring capabilities including bulk operations, market data integration, and trading workflow integration.

**Key Interfaces:**
- `addToWatchlist(tokenAddress, metadata?)` - Single token addition with validation
- `bulkAddTokens(tokenList[])` - Multi-token import with deduplication
- `getWatchlistWithPrices()` - Enriched watchlist with current market data

**Dependencies:** Price Monitor for market data, Database for watchlist storage, Trading Engine for routing integration

**Technology Stack:** Express.js API routes, Prisma models, @solana/web3.js for address validation, integration with Price Monitor

## Notification System

**Responsibility:** Multi-channel alert delivery including Telegram integration, in-app notifications, and event-driven messaging for all trading activities.

**Key Interfaces:**
- `sendAlert(type, data, priority)` - Unified alert dispatch
- `configureTelegram(botToken, chatId)` - External notification setup
- `getNotificationHistory(filter?)` - Alert audit trail and management

**Dependencies:** Telegram Bot API, BullMQ for reliable delivery, Database for notification history and preferences

**Technology Stack:** Telegram Bot API integration, BullMQ job queues for delivery reliability, Redis for rate limiting, Express.js webhooks

## Frontend Dashboard

**Responsibility:** Real-time trading interface with position monitoring, manual controls, and responsive design for desktop and mobile access.

**Key Interfaces:**
- `<PositionCard />` - Individual position display with real-time updates
- `<TradingPanel />` - Token input, quote display, and buy execution
- `<WatchlistPage />` - Token research and monitoring interface

**Dependencies:** Backend API services, Zustand state management, real-time price updates via polling

**Technology Stack:** Next.js 14 with App Router, React components, shadcn/ui, Tailwind CSS, Zustand for state, SWR for data fetching
