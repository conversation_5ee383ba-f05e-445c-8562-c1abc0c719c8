# Backend Architecture

## Service Architecture

### Controller/Route Organization

```
src/
├── routes/
│   ├── index.ts          # Route aggregation and setup
│   ├── trades.ts         # Trading operations (/api/trades/*)
│   ├── positions.ts      # Position management (/api/positions/*)
│   ├── watchlist.ts      # Watchlist operations (/api/watchlist/*)
│   ├── health.ts         # System health checks (/api/health)
│   └── webhooks.ts       # External webhook handlers
├── controllers/
│   ├── TradeController.ts    # Trading logic coordination
│   ├── PositionController.ts # Position CRUD operations
│   ├── WatchlistController.ts # Watchlist management
│   └── SystemController.ts   # Health and status endpoints
├── services/
│   ├── TradingService.ts     # Core trading operations
│   ├── ExitStrategyService.ts # Automated exit logic
│   ├── PriceMonitorService.ts # Price monitoring and alerts
│   ├── NotificationService.ts # Alert delivery
│   └── ExternalApiService.ts  # Jupiter, Helius, CMC integration
├── repositories/
│   ├── PositionRepository.ts  # Position data access
│   ├── TransactionRepository.ts # Transaction records
│   ├── WatchlistRepository.ts  # Watchlist data access
│   └── PriceRepository.ts     # Time-series price data
├── jobs/
│   ├── workers/              # BullMQ job processors
│   │   ├── priceMonitor.ts   # Price monitoring job
│   │   ├── exitExecution.ts  # Exit strategy execution
│   │   └── notifications.ts  # Notification delivery
│   ├── schedulers/           # Job scheduling logic
│   └── queues.ts            # Queue configuration
└── middleware/
    ├── auth.ts              # Authentication middleware
    ├── validation.ts        # Request validation
    ├── errorHandler.ts      # Global error handling
    └── rateLimit.ts         # API rate limiting
```

### Controller Template

```typescript
// controllers/PositionController.ts
export class PositionController {
  constructor(
    private positionService: PositionService,
    private exitStrategyService: ExitStrategyService,
    private notificationService: NotificationService
  ) {}
  
  async getAllPositions(req: Request, res: Response): Promise<void> {
    try {
      const { status = 'active' } = req.query;
      const positions = await this.positionService.getPositions(status as PositionStatus);
      
      res.json({
        data: positions,
        total: positions.length,
        status: status,
      });
    } catch (error) {
      throw new ApiError('Failed to fetch positions', 500, error);
    }
  }
  
  async updatePosition(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { exitStrategy, isActive } = req.body;
      
      // Validate position exists
      const position = await this.positionService.getById(id);
      if (!position) {
        throw new ApiError('Position not found', 404);
      }
      
      // Update exit strategy if provided
      if (exitStrategy) {
        await this.exitStrategyService.updateStrategy(id, exitStrategy);
      }
      
      // Update position status if provided
      if (typeof isActive === 'boolean') {
        await this.positionService.updateStatus(id, isActive ? 'active' : 'paused');
      }
      
      const updatedPosition = await this.positionService.getById(id);
      
      // Send notification for significant changes
      if (exitStrategy) {
        await this.notificationService.sendAlert({
          type: 'strategy_updated',
          message: `Exit strategy updated for ${position.tokenSymbol}`,
          data: { positionId: id, strategy: exitStrategy },
        });
      }
      
      res.json({
        data: updatedPosition,
        message: 'Position updated successfully',
      });
    } catch (error) {
      throw new ApiError('Failed to update position', 500, error);
    }
  }
  
  async closePosition(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const position = await this.positionService.getById(id);
      
      if (!position) {
        throw new ApiError('Position not found', 404);
      }
      
      if (position.status !== 'active') {
        throw new ApiError('Position is not active', 400);
      }
      
      // Queue immediate sell job with high priority
      const jobResult = await this.exitStrategyService.queueManualClose(id);
      
      res.json({
        message: 'Position close initiated',
        jobId: jobResult.id,
        estimatedTime: '5-15 seconds',
      });
    } catch (error) {
      throw new ApiError('Failed to initiate position close', 500, error);
    }
  }
  
  async emergencyCloseAll(req: Request, res: Response): Promise<void> {
    try {
      const activePositions = await this.positionService.getPositions('active');
      
      const closeJobs = await Promise.all(
        activePositions.map(position => 
          this.exitStrategyService.queueManualClose(position.id, 'emergency')
        )
      );
      
      await this.notificationService.sendAlert({
        type: 'emergency_close',
        message: `Emergency close initiated for ${activePositions.length} positions`,
        priority: 'critical',
      });
      
      res.json({
        message: `Emergency close initiated for ${activePositions.length} positions`,
        jobIds: closeJobs.map(job => job.id),
        estimatedTime: '15-60 seconds',
      });
    } catch (error) {
      throw new ApiError('Emergency close failed', 500, error);
    }
  }
}
```

## Database Architecture

### Schema Design

```sql
-- Core position tracking with audit trail
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(50) NOT NULL,
    token_symbol VARCHAR(20),
    token_name VARCHAR(100),
    amount_tokens DECIMAL(20,8) NOT NULL,
    entry_price DECIMAL(20,8) NOT NULL,
    entry_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status VARCHAR(20) NOT NULL DEFAULT 'active'
        CHECK (status IN ('active', 'closing', 'closed', 'paused')),
    
    -- Performance tracking
    highest_price DECIMAL(20,8), -- For trailing stops
    lowest_price DECIMAL(20,8),  -- For analytics
    realized_pnl DECIMAL(20,8) DEFAULT 0, -- Completed exits only
    
    -- Metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    closed_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT positive_amount CHECK (amount_tokens > 0),
    CONSTRAINT positive_entry_price CHECK (entry_price > 0)
);

-- Exit strategy configurations with JSONB for flexibility
CREATE TABLE exit_strategies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL REFERENCES positions(id) ON DELETE CASCADE,
    
    -- Serialized strategy configuration
    take_profit_tiers JSONB, -- [{tierNumber, targetPrice, percentageToSell, status, executedAt}]
    stop_loss JSONB,         -- {triggerPrice, percentageToSell, status, executedAt}
    trailing_stop JSONB,     -- {trailDistance, highestPrice, currentStopPrice, isActive, adjustments[]}
    moon_bag JSONB,          -- {percentage, minPrice, isActive}
    
    -- Configuration metadata
    is_active BOOLEAN NOT NULL DEFAULT true,
    preset_name VARCHAR(50), -- Reference to saved preset
    
    -- Audit trail
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(position_id), -- One strategy per position
    CONSTRAINT valid_strategy CHECK (
        take_profit_tiers IS NOT NULL OR 
        stop_loss IS NOT NULL OR 
        trailing_stop IS NOT NULL
    )
);

-- Time-series price data optimized for monitoring
CREATE TABLE price_snapshots (
    timestamp TIMESTAMPTZ NOT NULL,
    token_address VARCHAR(50) NOT NULL,
    price_usd DECIMAL(20,8) NOT NULL,
    volume_24h DECIMAL(20,2),
    market_cap DECIMAL(20,2),
    price_change_1h DECIMAL(8,4),
    price_change_24h DECIMAL(8,4),
    liquidity DECIMAL(20,2),
    fdv DECIMAL(20,2),
    source VARCHAR(20) NOT NULL,
    
    -- TimescaleDB optimization
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT positive_price CHECK (price_usd > 0),
    CONSTRAINT valid_source CHECK (source IN ('cmc', 'jupiter', 'helius', 'birdeye'))
);

-- Convert to TimescaleDB hypertable for time-series optimization
SELECT create_hypertable('price_snapshots', 'timestamp');

-- Data retention policy (keep 90 days of price data)
SELECT add_retention_policy('price_snapshots', INTERVAL '90 days');
```

### Data Access Layer

```typescript
// repositories/PositionRepository.ts
export class PositionRepository {
  constructor(private prisma: PrismaClient) {}
  
  async create(data: CreatePositionData): Promise<Position> {
    return this.prisma.position.create({
      data: {
        ...data,
        status: 'active',
        entryTimestamp: new Date(),
      },
      include: {
        exitStrategy: true,
        transactions: true,
      },
    });
  }
  
  async findByStatus(status: PositionStatus): Promise<Position[]> {
    return this.prisma.position.findMany({
      where: { status },
      include: {
        exitStrategy: true,
        transactions: {
          orderBy: { createdAt: 'desc' },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }
  
  async findById(id: string): Promise<Position | null> {
    return this.prisma.position.findUnique({
      where: { id },
      include: {
        exitStrategy: true,
        transactions: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  }
  
  async updatePriceTracking(
    id: string, 
    currentPrice: Decimal,
    updateHighest: boolean = true
  ): Promise<void> {
    const position = await this.findById(id);
    if (!position) throw new Error('Position not found');
    
    const updates: Partial<Position> = {
      updatedAt: new Date(),
    };
    
    // Track highest price for trailing stops
    if (updateHighest && (!position.highestPrice || currentPrice.gt(position.highestPrice))) {
      updates.highestPrice = currentPrice;
    }
    
    // Track lowest price for analytics
    if (!position.lowestPrice || currentPrice.lt(position.lowestPrice)) {
      updates.lowestPrice = currentPrice;
    }
    
    await this.prisma.position.update({
      where: { id },
      data: updates,
    });
  }
  
  async closePosition(id: string, realizedPnL: Decimal): Promise<Position> {
    return this.prisma.position.update({
      where: { id },
      data: {
        status: 'closed',
        realizedPnl: realizedPnL,
        closedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }
  
  // Advanced queries for analytics
  async getPositionMetrics(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<PositionMetrics> {
    const since = new Date();
    const days = timeframe === 'day' ? 1 : timeframe === 'week' ? 7 : 30;
    since.setDate(since.getDate() - days);
    
    const [totalPositions, activePositions, closedPositions] = await Promise.all([
      this.prisma.position.count({
        where: { createdAt: { gte: since } },
      }),
      this.prisma.position.count({
        where: { status: 'active' },
      }),
      this.prisma.position.findMany({
        where: {
          status: 'closed',
          closedAt: { gte: since },
        },
        select: { realizedPnl: true },
      }),
    ]);
    
    const totalRealizedPnL = closedPositions.reduce(
      (sum, pos) => sum.add(pos.realizedPnl || new Decimal(0)),
      new Decimal(0)
    );
    
    const winningTrades = closedPositions.filter(
      pos => pos.realizedPnl && pos.realizedPnl.gt(0)
    ).length;
    
    return {
      totalPositions,
      activePositions,
      closedPositions: closedPositions.length,
      totalRealizedPnL,
      winRate: closedPositions.length > 0 ? winningTrades / closedPositions.length : 0,
      timeframe,
    };
  }
}
```

## Authentication and Authorization

### Auth Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant S as Session Store
    participant D as Database

    Note over U,D: Session-Based Authentication (Single User)
    U->>F: Access application
    F->>A: GET /api/auth/status
    A->>S: Check session cookie
    
    alt No valid session
        S-->>A: No session found
        A-->>F: 401 Unauthorized
        F-->>U: Redirect to login
        
        U->>F: Enter credentials
        F->>A: POST /api/auth/login
        A->>D: Validate credentials
        D-->>A: User data
        A->>S: Create session
        S-->>A: Session ID
        A-->>F: Set session cookie + user data
        F-->>U: Redirect to dashboard
    else Valid session exists
        S-->>A: Session data
        A-->>F: User data
        F-->>U: Show authenticated interface
    end
    
    Note over U,D: Authenticated Request
    U->>F: Trading action
    F->>A: POST /api/trades/buy (with session cookie)
    A->>S: Validate session
    S-->>A: Session valid
    A->>A: Process trading request
    A-->>F: Trading response
    F-->>U: Update UI
```

### Middleware/Guards

```typescript
// middleware/auth.ts
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    permissions: string[];
  };
  session?: {
    id: string;
    expiresAt: Date;
  };
}

export async function requireAuth(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const sessionId = req.cookies.sessionId;
    
    if (!sessionId) {
      return res.status(401).json({
        error: {
          code: 'NO_SESSION',
          message: 'Authentication required',
        },
      });
    }
    
    // Check session in Redis (fast lookup)
    const sessionData = await redis.get(`session:${sessionId}`);
    
    if (!sessionData) {
      return res.status(401).json({
        error: {
          code: 'INVALID_SESSION',
          message: 'Session expired or invalid',
        },
      });
    }
    
    const session = JSON.parse(sessionData);
    
    // Validate session expiration
    if (new Date() > new Date(session.expiresAt)) {
      await redis.del(`session:${sessionId}`);
      return res.status(401).json({
        error: {
          code: 'SESSION_EXPIRED',
          message: 'Session has expired',
        },
      });
    }
    
    // Extend session expiration on activity
    const extendedSession = {
      ...session,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };
    
    await redis.setex(
      `session:${sessionId}`,
      24 * 60 * 60, // 24 hours in seconds
      JSON.stringify(extendedSession)
    );
    
    // Attach user data to request
    req.user = session.user;
    req.session = {
      id: sessionId,
      expiresAt: new Date(extendedSession.expiresAt),
    };
    
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      error: {
        code: 'AUTH_ERROR',
        message: 'Authentication system error',
      },
    });
  }
}

// Specialized middleware for trading operations
export async function requireTradingPermission(
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  if (!req.user) {
    return res.status(401).json({
      error: {
        code: 'NO_USER',
        message: 'User not authenticated',
      },
    });
  }
  
  // For single-user app, all authenticated users have trading permission
  // In multi-user version, check permissions array
  const hasPermission = req.user.permissions.includes('trading') || 
                       req.user.permissions.includes('admin');
  
  if (!hasPermission) {
    return res.status(403).json({
      error: {
        code: 'INSUFFICIENT_PERMISSIONS',
        message: 'Trading permission required',
      },
    });
  }
  
  next();
}

// Routes setup with middleware
// routes/trades.ts
const router = express.Router();

router.use(requireAuth); // All trading routes require authentication
router.use(requireTradingPermission); // All trading routes require trading permission

router.post('/quote', tradeController.getQuote);
router.post('/buy', tradeController.executeBuy);
```
