# Overview

This section provides comprehensive instructions for setting up the Solana Trading App locally on your development machine. This setup creates a fully functional local environment that replicates the production architecture behavior, allowing complete development and testing of all Solana trading features without any external deployments.

**Goal:** Create a self-contained local development environment with Next.js frontend (localhost:3000), Express API (localhost:3001), PostgreSQL with TimescaleDB, Redis, and BullMQ job processing.
