# Source Tree

## Project Structure Overview

The MemeTrader Pro application follows a modern monorepo structure using Turborepo for efficient builds and dependency management. The project is organized into clearly separated concerns with shared packages for common functionality.

```
solana-trading-app/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       ├── ci.yaml
│       └── deploy.yaml
├── apps/                       # Application packages
│   ├── web/                    # Frontend application
│   │   ├── src/
│   │   │   ├── app/            # Next.js App Router pages
│   │   │   │   ├── layout.tsx
│   │   │   │   ├── page.tsx
│   │   │   │   ├── trading/
│   │   │   │   ├── positions/
│   │   │   │   ├── watchlist/
│   │   │   │   ├── settings/
│   │   │   │   └── api/        # API route handlers
│   │   │   ├── components/     # UI components
│   │   │   │   ├── ui/         # shadcn/ui components
│   │   │   │   ├── trading/
│   │   │   │   ├── positions/
│   │   │   │   ├── watchlist/
│   │   │   │   └── layout/
│   │   │   ├── hooks/          # Custom React hooks
│   │   │   │   ├── useTrading.ts
│   │   │   │   ├── usePositions.ts
│   │   │   │   ├── useWatchlist.ts
│   │   │   │   └── useRealTimePrice.ts
│   │   │   ├── services/       # API client services
│   │   │   │   ├── api-client.ts
│   │   │   │   ├── trading.ts
│   │   │   │   ├── positions.ts
│   │   │   │   └── watchlist.ts
│   │   │   ├── stores/         # Zustand state management
│   │   │   │   ├── tradingStore.ts
│   │   │   │   ├── positionStore.ts
│   │   │   │   ├── watchlistStore.ts
│   │   │   │   └── systemStore.ts
│   │   │   ├── lib/            # Frontend utilities
│   │   │   │   ├── utils.ts
│   │   │   │   ├── constants.ts
│   │   │   │   ├── formatters.ts
│   │   │   │   └── validators.ts
│   │   │   └── styles/         # Global styles/themes
│   │   │       ├── globals.css
│   │   │       └── components.css
│   │   ├── public/             # Static assets
│   │   │   ├── favicon.ico
│   │   │   └── images/
│   │   ├── tests/              # Frontend tests
│   │   │   ├── components/
│   │   │   ├── hooks/
│   │   │   ├── services/
│   │   │   └── utils/
│   │   ├── next.config.js
│   │   ├── tailwind.config.js
│   │   ├── vitest.config.ts
│   │   └── package.json
│   └── api/                    # Backend application
│       ├── src/
│       │   ├── routes/         # API routes/controllers
│       │   │   ├── index.ts
│       │   │   ├── trades.ts
│       │   │   ├── positions.ts
│       │   │   ├── watchlist.ts
│       │   │   ├── health.ts
│       │   │   └── webhooks.ts
│       │   ├── controllers/    # Request handlers
│       │   │   ├── TradeController.ts
│       │   │   ├── PositionController.ts
│       │   │   ├── WatchlistController.ts
│       │   │   └── SystemController.ts
│       │   ├── services/       # Business logic
│       │   │   ├── TradingService.ts
│       │   │   ├── ExitStrategyService.ts
│       │   │   ├── PriceMonitorService.ts
│       │   │   ├── NotificationService.ts
│       │   │   └── ExternalApiService.ts
│       │   ├── repositories/   # Data access layer
│       │   │   ├── PositionRepository.ts
│       │   │   ├── TransactionRepository.ts
│       │   │   ├── WatchlistRepository.ts
│       │   │   └── PriceRepository.ts
│       │   ├── jobs/           # BullMQ job processing
│       │   │   ├── workers/
│       │   │   │   ├── priceMonitor.ts
│       │   │   │   ├── exitExecution.ts
│       │   │   │   └── notifications.ts
│       │   │   ├── schedulers/
│       │   │   └── queues.ts
│       │   ├── middleware/     # Express middleware
│       │   │   ├── auth.ts
│       │   │   ├── validation.ts
│       │   │   ├── errorHandler.ts
│       │   │   └── rateLimit.ts
│       │   ├── lib/            # Backend utilities
│       │   │   ├── database.ts
│       │   │   ├── redis.ts
│       │   │   ├── logger.ts
│       │   │   └── config.ts
│       │   ├── types/          # TypeScript definitions
│       │   │   ├── api.ts
│       │   │   ├── jobs.ts
│       │   │   └── external.ts
│       │   └── server.ts       # Express server entry point
│       ├── tests/              # Backend tests
│       │   ├── integration/
│       │   ├── unit/
│       │   └── fixtures/
│       ├── prisma/             # Database schema and migrations
│       │   ├── schema.prisma
│       │   ├── migrations/
│       │   └── seed.ts
│       ├── vitest.config.ts
│       └── package.json
├── packages/                   # Shared packages
│   ├── shared/                 # Shared types/utilities
│   │   ├── src/
│   │   │   ├── types/          # TypeScript interfaces
│   │   │   │   ├── position.ts
│   │   │   │   ├── trading.ts
│   │   │   │   ├── watchlist.ts
│   │   │   │   ├── external-api.ts
│   │   │   │   └── index.ts
│   │   │   ├── constants/      # Shared constants
│   │   │   │   ├── trading.ts
│   │   │   │   ├── api.ts
│   │   │   │   └── index.ts
│   │   │   ├── utils/          # Shared utilities
│   │   │   │   ├── validation.ts
│   │   │   │   ├── formatters.ts
│   │   │   │   ├── calculations.ts
│   │   │   │   └── index.ts
│   │   │   └── index.ts
│   │   ├── tests/
│   │   └── package.json
│   ├── ui/                     # Shared UI components
│   │   ├── src/
│   │   │   ├── components/
│   │   │   │   ├── charts/
│   │   │   │   └── forms/
│   │   │   └── index.ts
│   │   └── package.json
│   └── config/                 # Shared configuration
│       ├── eslint/
│       │   ├── base.js
│       │   ├── react.js
│       │   └── node.js
│       ├── typescript/
│       │   ├── base.json
│       │   ├── nextjs.json
│       │   └── node.json
│       ├── tailwind/
│       │   └── base.js
│       └── vitest/
│           └── base.ts
├── infrastructure/             # IaC definitions
│   ├── railway/
│   │   ├── railway.json
│   │   └── database.json
│   ├── vercel/
│   │   └── vercel.json
│   └── docker/
│       ├── Dockerfile.api
│       └── docker-compose.yml
├── scripts/                    # Build/deploy scripts
│   ├── setup.sh
│   ├── build.sh
│   ├── test.sh
│   └── deploy.sh
├── docs/                       # Documentation
│   ├── prd.md
│   ├── architecture.md
│   ├── api-reference.md
│   └── deployment.md
├── .env.example                # Environment template
├── .gitignore
├── package.json                # Root package.json
├── tsconfig.json              # Root TypeScript config
└── README.md
```

## Key Architectural Decisions

### Monorepo Structure
- **apps/**: Contains deployable applications (web frontend, api backend)
- **packages/**: Shared libraries and configurations
- **infrastructure/**: Deployment and infrastructure definitions

### Frontend Organization (apps/web/)
- **Next.js App Router**: Modern file-based routing with server components
- **Component Structure**: Domain-driven organization (trading/, positions/, watchlist/)
- **State Management**: Zustand stores for different domains
- **Services Layer**: API client abstractions
- **Hooks**: Custom React hooks for business logic

### Backend Organization (apps/api/)
- **Layered Architecture**: Routes → Controllers → Services → Repositories
- **Job Processing**: BullMQ workers for background tasks
- **Database**: Prisma ORM with PostgreSQL
- **Middleware**: Authentication, validation, error handling

### Shared Packages
- **shared/**: Common TypeScript types and utilities
- **ui/**: Reusable UI components
- **config/**: Shared configuration for tools (ESLint, TypeScript, Tailwind)

This structure supports scalability, maintainability, and clear separation of concerns while enabling code sharing across the monorepo.